function Md(e, t) { for (var n = 0; n < t.length; n++) { const r = t[n]; if (typeof r != "string" && !Array.isArray(r)) { for (const l in r) if (l !== "default" && !(l in e)) { const i = Object.getOwnPropertyDescriptor(r, l); i && Object.defineProperty(e, l, i.get ? i : { enumerable: !0, get: () => r[l] }) } } } return Object.freeze(Object.defineProperty(e, Symbol.toStringTag, { value: "Module" })) } (function () { const t = document.createElement("link").relList; if (t && t.supports && t.supports("modulepreload")) return; for (const l of document.querySelectorAll('link[rel="modulepreload"]')) r(l); new MutationObserver(l => { for (const i of l) if (i.type === "childList") for (const a of i.addedNodes) a.tagName === "LINK" && a.rel === "modulepreload" && r(a) }).observe(document, { childList: !0, subtree: !0 }); function n(l) { const i = {}; return l.integrity && (i.integrity = l.integrity), l.referrerPolicy && (i.referrerPolicy = l.referrerPolicy), l.crossOrigin === "use-credentials" ? i.credentials = "include" : l.crossOrigin === "anonymous" ? i.credentials = "omit" : i.credentials = "same-origin", i } function r(l) { if (l.ep) return; l.ep = !0; const i = n(l); fetch(l.href, i) } })(); function Td(e) { return e && e.__esModule && Object.prototype.hasOwnProperty.call(e, "default") ? e.default : e } var Go = { exports: {} }, Ol = {}, Ko = { exports: {} }, R = {};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Sr = Symbol.for("react.element"), Dd = Symbol.for("react.portal"), Rd = Symbol.for("react.fragment"), Od = Symbol.for("react.strict_mode"), Ld = Symbol.for("react.profiler"), Id = Symbol.for("react.provider"), zd = Symbol.for("react.context"), Fd = Symbol.for("react.forward_ref"), $d = Symbol.for("react.suspense"), Ud = Symbol.for("react.memo"), Ad = Symbol.for("react.lazy"), Ca = Symbol.iterator; function Wd(e) { return e === null || typeof e != "object" ? null : (e = Ca && e[Ca] || e["@@iterator"], typeof e == "function" ? e : null) } var Xo = { isMounted: function () { return !1 }, enqueueForceUpdate: function () { }, enqueueReplaceState: function () { }, enqueueSetState: function () { } }, Jo = Object.assign, Zo = {}; function Pn(e, t, n) { this.props = e, this.context = t, this.refs = Zo, this.updater = n || Xo } Pn.prototype.isReactComponent = {}; Pn.prototype.setState = function (e, t) { if (typeof e != "object" && typeof e != "function" && e != null) throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables."); this.updater.enqueueSetState(this, e, t, "setState") }; Pn.prototype.forceUpdate = function (e) { this.updater.enqueueForceUpdate(this, e, "forceUpdate") }; function eu() { } eu.prototype = Pn.prototype; function _s(e, t, n) { this.props = e, this.context = t, this.refs = Zo, this.updater = n || Xo } var bs = _s.prototype = new eu; bs.constructor = _s; Jo(bs, Pn.prototype); bs.isPureReactComponent = !0; var Ea = Array.isArray, tu = Object.prototype.hasOwnProperty, Ms = { current: null }, nu = { key: !0, ref: !0, __self: !0, __source: !0 }; function ru(e, t, n) { var r, l = {}, i = null, a = null; if (t != null) for (r in t.ref !== void 0 && (a = t.ref), t.key !== void 0 && (i = "" + t.key), t) tu.call(t, r) && !nu.hasOwnProperty(r) && (l[r] = t[r]); var u = arguments.length - 2; if (u === 1) l.children = n; else if (1 < u) { for (var o = Array(u), c = 0; c < u; c++)o[c] = arguments[c + 2]; l.children = o } if (e && e.defaultProps) for (r in u = e.defaultProps, u) l[r] === void 0 && (l[r] = u[r]); return { $$typeof: Sr, type: e, key: i, ref: a, props: l, _owner: Ms.current } } function Bd(e, t) { return { $$typeof: Sr, type: e.type, key: t, ref: e.ref, props: e.props, _owner: e._owner } } function Ts(e) { return typeof e == "object" && e !== null && e.$$typeof === Sr } function Hd(e) { var t = { "=": "=0", ":": "=2" }; return "$" + e.replace(/[=:]/g, function (n) { return t[n] }) } var _a = /\/+/g; function li(e, t) { return typeof e == "object" && e !== null && e.key != null ? Hd("" + e.key) : t.toString(36) } function Yr(e, t, n, r, l) { var i = typeof e; (i === "undefined" || i === "boolean") && (e = null); var a = !1; if (e === null) a = !0; else switch (i) { case "string": case "number": a = !0; break; case "object": switch (e.$$typeof) { case Sr: case Dd: a = !0 } }if (a) return a = e, l = l(a), e = r === "" ? "." + li(a, 0) : r, Ea(l) ? (n = "", e != null && (n = e.replace(_a, "$&/") + "/"), Yr(l, t, n, "", function (c) { return c })) : l != null && (Ts(l) && (l = Bd(l, n + (!l.key || a && a.key === l.key ? "" : ("" + l.key).replace(_a, "$&/") + "/") + e)), t.push(l)), 1; if (a = 0, r = r === "" ? "." : r + ":", Ea(e)) for (var u = 0; u < e.length; u++) { i = e[u]; var o = r + li(i, u); a += Yr(i, t, n, o, l) } else if (o = Wd(e), typeof o == "function") for (e = o.call(e), u = 0; !(i = e.next()).done;)i = i.value, o = r + li(i, u++), a += Yr(i, t, n, o, l); else if (i === "object") throw t = String(e), Error("Objects are not valid as a React child (found: " + (t === "[object Object]" ? "object with keys {" + Object.keys(e).join(", ") + "}" : t) + "). If you meant to render a collection of children, use an array instead."); return a } function Mr(e, t, n) { if (e == null) return e; var r = [], l = 0; return Yr(e, r, "", "", function (i) { return t.call(n, i, l++) }), r } function Vd(e) { if (e._status === -1) { var t = e._result; t = t(), t.then(function (n) { (e._status === 0 || e._status === -1) && (e._status = 1, e._result = n) }, function (n) { (e._status === 0 || e._status === -1) && (e._status = 2, e._result = n) }), e._status === -1 && (e._status = 0, e._result = t) } if (e._status === 1) return e._result.default; throw e._result } var de = { current: null }, qr = { transition: null }, Qd = { ReactCurrentDispatcher: de, ReactCurrentBatchConfig: qr, ReactCurrentOwner: Ms }; function lu() { throw Error("act(...) is not supported in production builds of React.") } R.Children = { map: Mr, forEach: function (e, t, n) { Mr(e, function () { t.apply(this, arguments) }, n) }, count: function (e) { var t = 0; return Mr(e, function () { t++ }), t }, toArray: function (e) { return Mr(e, function (t) { return t }) || [] }, only: function (e) { if (!Ts(e)) throw Error("React.Children.only expected to receive a single React element child."); return e } }; R.Component = Pn; R.Fragment = Rd; R.Profiler = Ld; R.PureComponent = _s; R.StrictMode = Od; R.Suspense = $d; R.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = Qd; R.act = lu; R.cloneElement = function (e, t, n) { if (e == null) throw Error("React.cloneElement(...): The argument must be a React element, but you passed " + e + "."); var r = Jo({}, e.props), l = e.key, i = e.ref, a = e._owner; if (t != null) { if (t.ref !== void 0 && (i = t.ref, a = Ms.current), t.key !== void 0 && (l = "" + t.key), e.type && e.type.defaultProps) var u = e.type.defaultProps; for (o in t) tu.call(t, o) && !nu.hasOwnProperty(o) && (r[o] = t[o] === void 0 && u !== void 0 ? u[o] : t[o]) } var o = arguments.length - 2; if (o === 1) r.children = n; else if (1 < o) { u = Array(o); for (var c = 0; c < o; c++)u[c] = arguments[c + 2]; r.children = u } return { $$typeof: Sr, type: e.type, key: l, ref: i, props: r, _owner: a } }; R.createContext = function (e) { return e = { $$typeof: zd, _currentValue: e, _currentValue2: e, _threadCount: 0, Provider: null, Consumer: null, _defaultValue: null, _globalName: null }, e.Provider = { $$typeof: Id, _context: e }, e.Consumer = e }; R.createElement = ru; R.createFactory = function (e) { var t = ru.bind(null, e); return t.type = e, t }; R.createRef = function () { return { current: null } }; R.forwardRef = function (e) { return { $$typeof: Fd, render: e } }; R.isValidElement = Ts; R.lazy = function (e) { return { $$typeof: Ad, _payload: { _status: -1, _result: e }, _init: Vd } }; R.memo = function (e, t) { return { $$typeof: Ud, type: e, compare: t === void 0 ? null : t } }; R.startTransition = function (e) { var t = qr.transition; qr.transition = {}; try { e() } finally { qr.transition = t } }; R.unstable_act = lu; R.useCallback = function (e, t) { return de.current.useCallback(e, t) }; R.useContext = function (e) { return de.current.useContext(e) }; R.useDebugValue = function () { }; R.useDeferredValue = function (e) { return de.current.useDeferredValue(e) }; R.useEffect = function (e, t) { return de.current.useEffect(e, t) }; R.useId = function () { return de.current.useId() }; R.useImperativeHandle = function (e, t, n) { return de.current.useImperativeHandle(e, t, n) }; R.useInsertionEffect = function (e, t) { return de.current.useInsertionEffect(e, t) }; R.useLayoutEffect = function (e, t) { return de.current.useLayoutEffect(e, t) }; R.useMemo = function (e, t) { return de.current.useMemo(e, t) }; R.useReducer = function (e, t, n) { return de.current.useReducer(e, t, n) }; R.useRef = function (e) { return de.current.useRef(e) }; R.useState = function (e) { return de.current.useState(e) }; R.useSyncExternalStore = function (e, t, n) { return de.current.useSyncExternalStore(e, t, n) }; R.useTransition = function () { return de.current.useTransition() }; R.version = "18.3.1"; Ko.exports = R; var N = Ko.exports; const Yd = Td(N), qd = Md({ __proto__: null, default: Yd }, [N]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Gd = N, Kd = Symbol.for("react.element"), Xd = Symbol.for("react.fragment"), Jd = Object.prototype.hasOwnProperty, Zd = Gd.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner, ef = { key: !0, ref: !0, __self: !0, __source: !0 }; function iu(e, t, n) { var r, l = {}, i = null, a = null; n !== void 0 && (i = "" + n), t.key !== void 0 && (i = "" + t.key), t.ref !== void 0 && (a = t.ref); for (r in t) Jd.call(t, r) && !ef.hasOwnProperty(r) && (l[r] = t[r]); if (e && e.defaultProps) for (r in t = e.defaultProps, t) l[r] === void 0 && (l[r] = t[r]); return { $$typeof: Kd, type: e, key: i, ref: a, props: l, _owner: Zd.current } } Ol.Fragment = Xd; Ol.jsx = iu; Ol.jsxs = iu; Go.exports = Ol; var s = Go.exports, su = { exports: {} }, ke = {}, au = { exports: {} }, ou = {};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function (e) { function t(_, M) { var D = _.length; _.push(M); e: for (; 0 < D;) { var q = D - 1 >>> 1, Z = _[q]; if (0 < l(Z, M)) _[q] = M, _[D] = Z, D = q; else break e } } function n(_) { return _.length === 0 ? null : _[0] } function r(_) { if (_.length === 0) return null; var M = _[0], D = _.pop(); if (D !== M) { _[0] = D; e: for (var q = 0, Z = _.length, _r = Z >>> 1; q < _r;) { var bt = 2 * (q + 1) - 1, ri = _[bt], Mt = bt + 1, br = _[Mt]; if (0 > l(ri, D)) Mt < Z && 0 > l(br, ri) ? (_[q] = br, _[Mt] = D, q = Mt) : (_[q] = ri, _[bt] = D, q = bt); else if (Mt < Z && 0 > l(br, D)) _[q] = br, _[Mt] = D, q = Mt; else break e } } return M } function l(_, M) { var D = _.sortIndex - M.sortIndex; return D !== 0 ? D : _.id - M.id } if (typeof performance == "object" && typeof performance.now == "function") { var i = performance; e.unstable_now = function () { return i.now() } } else { var a = Date, u = a.now(); e.unstable_now = function () { return a.now() - u } } var o = [], c = [], g = 1, m = null, h = 3, w = !1, y = !1, x = !1, k = typeof setTimeout == "function" ? setTimeout : null, f = typeof clearTimeout == "function" ? clearTimeout : null, d = typeof setImmediate < "u" ? setImmediate : null; typeof navigator < "u" && navigator.scheduling !== void 0 && navigator.scheduling.isInputPending !== void 0 && navigator.scheduling.isInputPending.bind(navigator.scheduling); function p(_) { for (var M = n(c); M !== null;) { if (M.callback === null) r(c); else if (M.startTime <= _) r(c), M.sortIndex = M.expirationTime, t(o, M); else break; M = n(c) } } function v(_) { if (x = !1, p(_), !y) if (n(o) !== null) y = !0, ti(j); else { var M = n(c); M !== null && ni(v, M.startTime - _) } } function j(_, M) { y = !1, x && (x = !1, f(b), b = -1), w = !0; var D = h; try { for (p(M), m = n(o); m !== null && (!(m.expirationTime > M) || _ && !xe());) { var q = m.callback; if (typeof q == "function") { m.callback = null, h = m.priorityLevel; var Z = q(m.expirationTime <= M); M = e.unstable_now(), typeof Z == "function" ? m.callback = Z : m === n(o) && r(o), p(M) } else r(o); m = n(o) } if (m !== null) var _r = !0; else { var bt = n(c); bt !== null && ni(v, bt.startTime - M), _r = !1 } return _r } finally { m = null, h = D, w = !1 } } var P = !1, C = null, b = -1, W = 5, T = -1; function xe() { return !(e.unstable_now() - T < W) } function Tn() { if (C !== null) { var _ = e.unstable_now(); T = _; var M = !0; try { M = C(!0, _) } finally { M ? Dn() : (P = !1, C = null) } } else P = !1 } var Dn; if (typeof d == "function") Dn = function () { d(Tn) }; else if (typeof MessageChannel < "u") { var Pa = new MessageChannel, bd = Pa.port2; Pa.port1.onmessage = Tn, Dn = function () { bd.postMessage(null) } } else Dn = function () { k(Tn, 0) }; function ti(_) { C = _, P || (P = !0, Dn()) } function ni(_, M) { b = k(function () { _(e.unstable_now()) }, M) } e.unstable_IdlePriority = 5, e.unstable_ImmediatePriority = 1, e.unstable_LowPriority = 4, e.unstable_NormalPriority = 3, e.unstable_Profiling = null, e.unstable_UserBlockingPriority = 2, e.unstable_cancelCallback = function (_) { _.callback = null }, e.unstable_continueExecution = function () { y || w || (y = !0, ti(j)) }, e.unstable_forceFrameRate = function (_) { 0 > _ || 125 < _ ? console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported") : W = 0 < _ ? Math.floor(1e3 / _) : 5 }, e.unstable_getCurrentPriorityLevel = function () { return h }, e.unstable_getFirstCallbackNode = function () { return n(o) }, e.unstable_next = function (_) { switch (h) { case 1: case 2: case 3: var M = 3; break; default: M = h }var D = h; h = M; try { return _() } finally { h = D } }, e.unstable_pauseExecution = function () { }, e.unstable_requestPaint = function () { }, e.unstable_runWithPriority = function (_, M) { switch (_) { case 1: case 2: case 3: case 4: case 5: break; default: _ = 3 }var D = h; h = _; try { return M() } finally { h = D } }, e.unstable_scheduleCallback = function (_, M, D) { var q = e.unstable_now(); switch (typeof D == "object" && D !== null ? (D = D.delay, D = typeof D == "number" && 0 < D ? q + D : q) : D = q, _) { case 1: var Z = -1; break; case 2: Z = 250; break; case 5: Z = 1073741823; break; case 4: Z = 1e4; break; default: Z = 5e3 }return Z = D + Z, _ = { id: g++, callback: M, priorityLevel: _, startTime: D, expirationTime: Z, sortIndex: -1 }, D > q ? (_.sortIndex = D, t(c, _), n(o) === null && _ === n(c) && (x ? (f(b), b = -1) : x = !0, ni(v, D - q))) : (_.sortIndex = Z, t(o, _), y || w || (y = !0, ti(j))), _ }, e.unstable_shouldYield = xe, e.unstable_wrapCallback = function (_) { var M = h; return function () { var D = h; h = M; try { return _.apply(this, arguments) } finally { h = D } } } })(ou); au.exports = ou; var tf = au.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var nf = N, Se = tf; function S(e) { for (var t = "https://reactjs.org/docs/error-decoder.html?invariant=" + e, n = 1; n < arguments.length; n++)t += "&args[]=" + encodeURIComponent(arguments[n]); return "Minified React error #" + e + "; visit " + t + " for the full message or use the non-minified dev environment for full errors and additional helpful warnings." } var uu = new Set, rr = {}; function Yt(e, t) { gn(e, t), gn(e + "Capture", t) } function gn(e, t) { for (rr[e] = t, e = 0; e < t.length; e++)uu.add(t[e]) } var Ze = !(typeof window > "u" || typeof window.document > "u" || typeof window.document.createElement > "u"), Di = Object.prototype.hasOwnProperty, rf = /^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/, ba = {}, Ma = {}; function lf(e) { return Di.call(Ma, e) ? !0 : Di.call(ba, e) ? !1 : rf.test(e) ? Ma[e] = !0 : (ba[e] = !0, !1) } function sf(e, t, n, r) { if (n !== null && n.type === 0) return !1; switch (typeof t) { case "function": case "symbol": return !0; case "boolean": return r ? !1 : n !== null ? !n.acceptsBooleans : (e = e.toLowerCase().slice(0, 5), e !== "data-" && e !== "aria-"); default: return !1 } } function af(e, t, n, r) { if (t === null || typeof t > "u" || sf(e, t, n, r)) return !0; if (r) return !1; if (n !== null) switch (n.type) { case 3: return !t; case 4: return t === !1; case 5: return isNaN(t); case 6: return isNaN(t) || 1 > t }return !1 } function fe(e, t, n, r, l, i, a) { this.acceptsBooleans = t === 2 || t === 3 || t === 4, this.attributeName = r, this.attributeNamespace = l, this.mustUseProperty = n, this.propertyName = e, this.type = t, this.sanitizeURL = i, this.removeEmptyString = a } var le = {}; "children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function (e) { le[e] = new fe(e, 0, !1, e, null, !1, !1) });[["acceptCharset", "accept-charset"], ["className", "class"], ["htmlFor", "for"], ["httpEquiv", "http-equiv"]].forEach(function (e) { var t = e[0]; le[t] = new fe(t, 1, !1, e[1], null, !1, !1) });["contentEditable", "draggable", "spellCheck", "value"].forEach(function (e) { le[e] = new fe(e, 2, !1, e.toLowerCase(), null, !1, !1) });["autoReverse", "externalResourcesRequired", "focusable", "preserveAlpha"].forEach(function (e) { le[e] = new fe(e, 2, !1, e, null, !1, !1) }); "allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function (e) { le[e] = new fe(e, 3, !1, e.toLowerCase(), null, !1, !1) });["checked", "multiple", "muted", "selected"].forEach(function (e) { le[e] = new fe(e, 3, !0, e, null, !1, !1) });["capture", "download"].forEach(function (e) { le[e] = new fe(e, 4, !1, e, null, !1, !1) });["cols", "rows", "size", "span"].forEach(function (e) { le[e] = new fe(e, 6, !1, e, null, !1, !1) });["rowSpan", "start"].forEach(function (e) { le[e] = new fe(e, 5, !1, e.toLowerCase(), null, !1, !1) }); var Ds = /[\-:]([a-z])/g; function Rs(e) { return e[1].toUpperCase() } "accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function (e) { var t = e.replace(Ds, Rs); le[t] = new fe(t, 1, !1, e, null, !1, !1) }); "xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function (e) { var t = e.replace(Ds, Rs); le[t] = new fe(t, 1, !1, e, "http://www.w3.org/1999/xlink", !1, !1) });["xml:base", "xml:lang", "xml:space"].forEach(function (e) { var t = e.replace(Ds, Rs); le[t] = new fe(t, 1, !1, e, "http://www.w3.org/XML/1998/namespace", !1, !1) });["tabIndex", "crossOrigin"].forEach(function (e) { le[e] = new fe(e, 1, !1, e.toLowerCase(), null, !1, !1) }); le.xlinkHref = new fe("xlinkHref", 1, !1, "xlink:href", "http://www.w3.org/1999/xlink", !0, !1);["src", "href", "action", "formAction"].forEach(function (e) { le[e] = new fe(e, 1, !1, e.toLowerCase(), null, !0, !0) }); function Os(e, t, n, r) { var l = le.hasOwnProperty(t) ? le[t] : null; (l !== null ? l.type !== 0 : r || !(2 < t.length) || t[0] !== "o" && t[0] !== "O" || t[1] !== "n" && t[1] !== "N") && (af(t, n, l, r) && (n = null), r || l === null ? lf(t) && (n === null ? e.removeAttribute(t) : e.setAttribute(t, "" + n)) : l.mustUseProperty ? e[l.propertyName] = n === null ? l.type === 3 ? !1 : "" : n : (t = l.attributeName, r = l.attributeNamespace, n === null ? e.removeAttribute(t) : (l = l.type, n = l === 3 || l === 4 && n === !0 ? "" : "" + n, r ? e.setAttributeNS(r, t, n) : e.setAttribute(t, n)))) } var rt = nf.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED, Tr = Symbol.for("react.element"), Xt = Symbol.for("react.portal"), Jt = Symbol.for("react.fragment"), Ls = Symbol.for("react.strict_mode"), Ri = Symbol.for("react.profiler"), cu = Symbol.for("react.provider"), du = Symbol.for("react.context"), Is = Symbol.for("react.forward_ref"), Oi = Symbol.for("react.suspense"), Li = Symbol.for("react.suspense_list"), zs = Symbol.for("react.memo"), at = Symbol.for("react.lazy"), fu = Symbol.for("react.offscreen"), Ta = Symbol.iterator; function Rn(e) { return e === null || typeof e != "object" ? null : (e = Ta && e[Ta] || e["@@iterator"], typeof e == "function" ? e : null) } var Q = Object.assign, ii; function Bn(e) {
    if (ii === void 0) try { throw Error() } catch (n) { var t = n.stack.trim().match(/\n( *(at )?)/); ii = t && t[1] || "" } return `
`+ ii + e
} var si = !1; function ai(e, t) {
    if (!e || si) return ""; si = !0; var n = Error.prepareStackTrace; Error.prepareStackTrace = void 0; try { if (t) if (t = function () { throw Error() }, Object.defineProperty(t.prototype, "props", { set: function () { throw Error() } }), typeof Reflect == "object" && Reflect.construct) { try { Reflect.construct(t, []) } catch (c) { var r = c } Reflect.construct(e, [], t) } else { try { t.call() } catch (c) { r = c } e.call(t.prototype) } else { try { throw Error() } catch (c) { r = c } e() } } catch (c) {
        if (c && r && typeof c.stack == "string") {
            for (var l = c.stack.split(`
`), i = r.stack.split(`
`), a = l.length - 1, u = i.length - 1; 1 <= a && 0 <= u && l[a] !== i[u];)u--; for (; 1 <= a && 0 <= u; a--, u--)if (l[a] !== i[u]) {
                if (a !== 1 || u !== 1) do if (a--, u--, 0 > u || l[a] !== i[u]) {
                    var o = `
`+ l[a].replace(" at new ", " at "); return e.displayName && o.includes("<anonymous>") && (o = o.replace("<anonymous>", e.displayName)), o
                } while (1 <= a && 0 <= u); break
            }
        }
    } finally { si = !1, Error.prepareStackTrace = n } return (e = e ? e.displayName || e.name : "") ? Bn(e) : ""
} function of(e) { switch (e.tag) { case 5: return Bn(e.type); case 16: return Bn("Lazy"); case 13: return Bn("Suspense"); case 19: return Bn("SuspenseList"); case 0: case 2: case 15: return e = ai(e.type, !1), e; case 11: return e = ai(e.type.render, !1), e; case 1: return e = ai(e.type, !0), e; default: return "" } } function Ii(e) { if (e == null) return null; if (typeof e == "function") return e.displayName || e.name || null; if (typeof e == "string") return e; switch (e) { case Jt: return "Fragment"; case Xt: return "Portal"; case Ri: return "Profiler"; case Ls: return "StrictMode"; case Oi: return "Suspense"; case Li: return "SuspenseList" }if (typeof e == "object") switch (e.$$typeof) { case du: return (e.displayName || "Context") + ".Consumer"; case cu: return (e._context.displayName || "Context") + ".Provider"; case Is: var t = e.render; return e = e.displayName, e || (e = t.displayName || t.name || "", e = e !== "" ? "ForwardRef(" + e + ")" : "ForwardRef"), e; case zs: return t = e.displayName || null, t !== null ? t : Ii(e.type) || "Memo"; case at: t = e._payload, e = e._init; try { return Ii(e(t)) } catch { } }return null } function uf(e) { var t = e.type; switch (e.tag) { case 24: return "Cache"; case 9: return (t.displayName || "Context") + ".Consumer"; case 10: return (t._context.displayName || "Context") + ".Provider"; case 18: return "DehydratedFragment"; case 11: return e = t.render, e = e.displayName || e.name || "", t.displayName || (e !== "" ? "ForwardRef(" + e + ")" : "ForwardRef"); case 7: return "Fragment"; case 5: return t; case 4: return "Portal"; case 3: return "Root"; case 6: return "Text"; case 16: return Ii(t); case 8: return t === Ls ? "StrictMode" : "Mode"; case 22: return "Offscreen"; case 12: return "Profiler"; case 21: return "Scope"; case 13: return "Suspense"; case 19: return "SuspenseList"; case 25: return "TracingMarker"; case 1: case 0: case 17: case 2: case 14: case 15: if (typeof t == "function") return t.displayName || t.name || null; if (typeof t == "string") return t }return null } function St(e) { switch (typeof e) { case "boolean": case "number": case "string": case "undefined": return e; case "object": return e; default: return "" } } function mu(e) { var t = e.type; return (e = e.nodeName) && e.toLowerCase() === "input" && (t === "checkbox" || t === "radio") } function cf(e) { var t = mu(e) ? "checked" : "value", n = Object.getOwnPropertyDescriptor(e.constructor.prototype, t), r = "" + e[t]; if (!e.hasOwnProperty(t) && typeof n < "u" && typeof n.get == "function" && typeof n.set == "function") { var l = n.get, i = n.set; return Object.defineProperty(e, t, { configurable: !0, get: function () { return l.call(this) }, set: function (a) { r = "" + a, i.call(this, a) } }), Object.defineProperty(e, t, { enumerable: n.enumerable }), { getValue: function () { return r }, setValue: function (a) { r = "" + a }, stopTracking: function () { e._valueTracker = null, delete e[t] } } } } function Dr(e) { e._valueTracker || (e._valueTracker = cf(e)) } function pu(e) { if (!e) return !1; var t = e._valueTracker; if (!t) return !0; var n = t.getValue(), r = ""; return e && (r = mu(e) ? e.checked ? "true" : "false" : e.value), e = r, e !== n ? (t.setValue(e), !0) : !1 } function il(e) { if (e = e || (typeof document < "u" ? document : void 0), typeof e > "u") return null; try { return e.activeElement || e.body } catch { return e.body } } function zi(e, t) { var n = t.checked; return Q({}, t, { defaultChecked: void 0, defaultValue: void 0, value: void 0, checked: n ?? e._wrapperState.initialChecked }) } function Da(e, t) { var n = t.defaultValue == null ? "" : t.defaultValue, r = t.checked != null ? t.checked : t.defaultChecked; n = St(t.value != null ? t.value : n), e._wrapperState = { initialChecked: r, initialValue: n, controlled: t.type === "checkbox" || t.type === "radio" ? t.checked != null : t.value != null } } function hu(e, t) { t = t.checked, t != null && Os(e, "checked", t, !1) } function Fi(e, t) { hu(e, t); var n = St(t.value), r = t.type; if (n != null) r === "number" ? (n === 0 && e.value === "" || e.value != n) && (e.value = "" + n) : e.value !== "" + n && (e.value = "" + n); else if (r === "submit" || r === "reset") { e.removeAttribute("value"); return } t.hasOwnProperty("value") ? $i(e, t.type, n) : t.hasOwnProperty("defaultValue") && $i(e, t.type, St(t.defaultValue)), t.checked == null && t.defaultChecked != null && (e.defaultChecked = !!t.defaultChecked) } function Ra(e, t, n) { if (t.hasOwnProperty("value") || t.hasOwnProperty("defaultValue")) { var r = t.type; if (!(r !== "submit" && r !== "reset" || t.value !== void 0 && t.value !== null)) return; t = "" + e._wrapperState.initialValue, n || t === e.value || (e.value = t), e.defaultValue = t } n = e.name, n !== "" && (e.name = ""), e.defaultChecked = !!e._wrapperState.initialChecked, n !== "" && (e.name = n) } function $i(e, t, n) { (t !== "number" || il(e.ownerDocument) !== e) && (n == null ? e.defaultValue = "" + e._wrapperState.initialValue : e.defaultValue !== "" + n && (e.defaultValue = "" + n)) } var Hn = Array.isArray; function cn(e, t, n, r) { if (e = e.options, t) { t = {}; for (var l = 0; l < n.length; l++)t["$" + n[l]] = !0; for (n = 0; n < e.length; n++)l = t.hasOwnProperty("$" + e[n].value), e[n].selected !== l && (e[n].selected = l), l && r && (e[n].defaultSelected = !0) } else { for (n = "" + St(n), t = null, l = 0; l < e.length; l++) { if (e[l].value === n) { e[l].selected = !0, r && (e[l].defaultSelected = !0); return } t !== null || e[l].disabled || (t = e[l]) } t !== null && (t.selected = !0) } } function Ui(e, t) { if (t.dangerouslySetInnerHTML != null) throw Error(S(91)); return Q({}, t, { value: void 0, defaultValue: void 0, children: "" + e._wrapperState.initialValue }) } function Oa(e, t) { var n = t.value; if (n == null) { if (n = t.children, t = t.defaultValue, n != null) { if (t != null) throw Error(S(92)); if (Hn(n)) { if (1 < n.length) throw Error(S(93)); n = n[0] } t = n } t == null && (t = ""), n = t } e._wrapperState = { initialValue: St(n) } } function gu(e, t) { var n = St(t.value), r = St(t.defaultValue); n != null && (n = "" + n, n !== e.value && (e.value = n), t.defaultValue == null && e.defaultValue !== n && (e.defaultValue = n)), r != null && (e.defaultValue = "" + r) } function La(e) { var t = e.textContent; t === e._wrapperState.initialValue && t !== "" && t !== null && (e.value = t) } function yu(e) { switch (e) { case "svg": return "http://www.w3.org/2000/svg"; case "math": return "http://www.w3.org/1998/Math/MathML"; default: return "http://www.w3.org/1999/xhtml" } } function Ai(e, t) { return e == null || e === "http://www.w3.org/1999/xhtml" ? yu(t) : e === "http://www.w3.org/2000/svg" && t === "foreignObject" ? "http://www.w3.org/1999/xhtml" : e } var Rr, vu = function (e) { return typeof MSApp < "u" && MSApp.execUnsafeLocalFunction ? function (t, n, r, l) { MSApp.execUnsafeLocalFunction(function () { return e(t, n, r, l) }) } : e }(function (e, t) { if (e.namespaceURI !== "http://www.w3.org/2000/svg" || "innerHTML" in e) e.innerHTML = t; else { for (Rr = Rr || document.createElement("div"), Rr.innerHTML = "<svg>" + t.valueOf().toString() + "</svg>", t = Rr.firstChild; e.firstChild;)e.removeChild(e.firstChild); for (; t.firstChild;)e.appendChild(t.firstChild) } }); function lr(e, t) { if (t) { var n = e.firstChild; if (n && n === e.lastChild && n.nodeType === 3) { n.nodeValue = t; return } } e.textContent = t } var Yn = { animationIterationCount: !0, aspectRatio: !0, borderImageOutset: !0, borderImageSlice: !0, borderImageWidth: !0, boxFlex: !0, boxFlexGroup: !0, boxOrdinalGroup: !0, columnCount: !0, columns: !0, flex: !0, flexGrow: !0, flexPositive: !0, flexShrink: !0, flexNegative: !0, flexOrder: !0, gridArea: !0, gridRow: !0, gridRowEnd: !0, gridRowSpan: !0, gridRowStart: !0, gridColumn: !0, gridColumnEnd: !0, gridColumnSpan: !0, gridColumnStart: !0, fontWeight: !0, lineClamp: !0, lineHeight: !0, opacity: !0, order: !0, orphans: !0, tabSize: !0, widows: !0, zIndex: !0, zoom: !0, fillOpacity: !0, floodOpacity: !0, stopOpacity: !0, strokeDasharray: !0, strokeDashoffset: !0, strokeMiterlimit: !0, strokeOpacity: !0, strokeWidth: !0 }, df = ["Webkit", "ms", "Moz", "O"]; Object.keys(Yn).forEach(function (e) { df.forEach(function (t) { t = t + e.charAt(0).toUpperCase() + e.substring(1), Yn[t] = Yn[e] }) }); function xu(e, t, n) { return t == null || typeof t == "boolean" || t === "" ? "" : n || typeof t != "number" || t === 0 || Yn.hasOwnProperty(e) && Yn[e] ? ("" + t).trim() : t + "px" } function wu(e, t) { e = e.style; for (var n in t) if (t.hasOwnProperty(n)) { var r = n.indexOf("--") === 0, l = xu(n, t[n], r); n === "float" && (n = "cssFloat"), r ? e.setProperty(n, l) : e[n] = l } } var ff = Q({ menuitem: !0 }, { area: !0, base: !0, br: !0, col: !0, embed: !0, hr: !0, img: !0, input: !0, keygen: !0, link: !0, meta: !0, param: !0, source: !0, track: !0, wbr: !0 }); function Wi(e, t) { if (t) { if (ff[e] && (t.children != null || t.dangerouslySetInnerHTML != null)) throw Error(S(137, e)); if (t.dangerouslySetInnerHTML != null) { if (t.children != null) throw Error(S(60)); if (typeof t.dangerouslySetInnerHTML != "object" || !("__html" in t.dangerouslySetInnerHTML)) throw Error(S(61)) } if (t.style != null && typeof t.style != "object") throw Error(S(62)) } } function Bi(e, t) { if (e.indexOf("-") === -1) return typeof t.is == "string"; switch (e) { case "annotation-xml": case "color-profile": case "font-face": case "font-face-src": case "font-face-uri": case "font-face-format": case "font-face-name": case "missing-glyph": return !1; default: return !0 } } var Hi = null; function Fs(e) { return e = e.target || e.srcElement || window, e.correspondingUseElement && (e = e.correspondingUseElement), e.nodeType === 3 ? e.parentNode : e } var Vi = null, dn = null, fn = null; function Ia(e) { if (e = Cr(e)) { if (typeof Vi != "function") throw Error(S(280)); var t = e.stateNode; t && (t = $l(t), Vi(e.stateNode, e.type, t)) } } function Nu(e) { dn ? fn ? fn.push(e) : fn = [e] : dn = e } function ju() { if (dn) { var e = dn, t = fn; if (fn = dn = null, Ia(e), t) for (e = 0; e < t.length; e++)Ia(t[e]) } } function Su(e, t) { return e(t) } function ku() { } var oi = !1; function Pu(e, t, n) { if (oi) return e(t, n); oi = !0; try { return Su(e, t, n) } finally { oi = !1, (dn !== null || fn !== null) && (ku(), ju()) } } function ir(e, t) { var n = e.stateNode; if (n === null) return null; var r = $l(n); if (r === null) return null; n = r[t]; e: switch (t) { case "onClick": case "onClickCapture": case "onDoubleClick": case "onDoubleClickCapture": case "onMouseDown": case "onMouseDownCapture": case "onMouseMove": case "onMouseMoveCapture": case "onMouseUp": case "onMouseUpCapture": case "onMouseEnter": (r = !r.disabled) || (e = e.type, r = !(e === "button" || e === "input" || e === "select" || e === "textarea")), e = !r; break e; default: e = !1 }if (e) return null; if (n && typeof n != "function") throw Error(S(231, t, typeof n)); return n } var Qi = !1; if (Ze) try { var On = {}; Object.defineProperty(On, "passive", { get: function () { Qi = !0 } }), window.addEventListener("test", On, On), window.removeEventListener("test", On, On) } catch { Qi = !1 } function mf(e, t, n, r, l, i, a, u, o) { var c = Array.prototype.slice.call(arguments, 3); try { t.apply(n, c) } catch (g) { this.onError(g) } } var qn = !1, sl = null, al = !1, Yi = null, pf = { onError: function (e) { qn = !0, sl = e } }; function hf(e, t, n, r, l, i, a, u, o) { qn = !1, sl = null, mf.apply(pf, arguments) } function gf(e, t, n, r, l, i, a, u, o) { if (hf.apply(this, arguments), qn) { if (qn) { var c = sl; qn = !1, sl = null } else throw Error(S(198)); al || (al = !0, Yi = c) } } function qt(e) { var t = e, n = e; if (e.alternate) for (; t.return;)t = t.return; else { e = t; do t = e, t.flags & 4098 && (n = t.return), e = t.return; while (e) } return t.tag === 3 ? n : null } function Cu(e) { if (e.tag === 13) { var t = e.memoizedState; if (t === null && (e = e.alternate, e !== null && (t = e.memoizedState)), t !== null) return t.dehydrated } return null } function za(e) { if (qt(e) !== e) throw Error(S(188)) } function yf(e) { var t = e.alternate; if (!t) { if (t = qt(e), t === null) throw Error(S(188)); return t !== e ? null : e } for (var n = e, r = t; ;) { var l = n.return; if (l === null) break; var i = l.alternate; if (i === null) { if (r = l.return, r !== null) { n = r; continue } break } if (l.child === i.child) { for (i = l.child; i;) { if (i === n) return za(l), e; if (i === r) return za(l), t; i = i.sibling } throw Error(S(188)) } if (n.return !== r.return) n = l, r = i; else { for (var a = !1, u = l.child; u;) { if (u === n) { a = !0, n = l, r = i; break } if (u === r) { a = !0, r = l, n = i; break } u = u.sibling } if (!a) { for (u = i.child; u;) { if (u === n) { a = !0, n = i, r = l; break } if (u === r) { a = !0, r = i, n = l; break } u = u.sibling } if (!a) throw Error(S(189)) } } if (n.alternate !== r) throw Error(S(190)) } if (n.tag !== 3) throw Error(S(188)); return n.stateNode.current === n ? e : t } function Eu(e) { return e = yf(e), e !== null ? _u(e) : null } function _u(e) { if (e.tag === 5 || e.tag === 6) return e; for (e = e.child; e !== null;) { var t = _u(e); if (t !== null) return t; e = e.sibling } return null } var bu = Se.unstable_scheduleCallback, Fa = Se.unstable_cancelCallback, vf = Se.unstable_shouldYield, xf = Se.unstable_requestPaint, G = Se.unstable_now, wf = Se.unstable_getCurrentPriorityLevel, $s = Se.unstable_ImmediatePriority, Mu = Se.unstable_UserBlockingPriority, ol = Se.unstable_NormalPriority, Nf = Se.unstable_LowPriority, Tu = Se.unstable_IdlePriority, Ll = null, Ve = null; function jf(e) { if (Ve && typeof Ve.onCommitFiberRoot == "function") try { Ve.onCommitFiberRoot(Ll, e, void 0, (e.current.flags & 128) === 128) } catch { } } var Ie = Math.clz32 ? Math.clz32 : Pf, Sf = Math.log, kf = Math.LN2; function Pf(e) { return e >>>= 0, e === 0 ? 32 : 31 - (Sf(e) / kf | 0) | 0 } var Or = 64, Lr = 4194304; function Vn(e) { switch (e & -e) { case 1: return 1; case 2: return 2; case 4: return 4; case 8: return 8; case 16: return 16; case 32: return 32; case 64: case 128: case 256: case 512: case 1024: case 2048: case 4096: case 8192: case 16384: case 32768: case 65536: case 131072: case 262144: case 524288: case 1048576: case 2097152: return e & 4194240; case 4194304: case 8388608: case 16777216: case 33554432: case 67108864: return e & 130023424; case 134217728: return 134217728; case 268435456: return 268435456; case 536870912: return 536870912; case 1073741824: return 1073741824; default: return e } } function ul(e, t) { var n = e.pendingLanes; if (n === 0) return 0; var r = 0, l = e.suspendedLanes, i = e.pingedLanes, a = n & 268435455; if (a !== 0) { var u = a & ~l; u !== 0 ? r = Vn(u) : (i &= a, i !== 0 && (r = Vn(i))) } else a = n & ~l, a !== 0 ? r = Vn(a) : i !== 0 && (r = Vn(i)); if (r === 0) return 0; if (t !== 0 && t !== r && !(t & l) && (l = r & -r, i = t & -t, l >= i || l === 16 && (i & 4194240) !== 0)) return t; if (r & 4 && (r |= n & 16), t = e.entangledLanes, t !== 0) for (e = e.entanglements, t &= r; 0 < t;)n = 31 - Ie(t), l = 1 << n, r |= e[n], t &= ~l; return r } function Cf(e, t) { switch (e) { case 1: case 2: case 4: return t + 250; case 8: case 16: case 32: case 64: case 128: case 256: case 512: case 1024: case 2048: case 4096: case 8192: case 16384: case 32768: case 65536: case 131072: case 262144: case 524288: case 1048576: case 2097152: return t + 5e3; case 4194304: case 8388608: case 16777216: case 33554432: case 67108864: return -1; case 134217728: case 268435456: case 536870912: case 1073741824: return -1; default: return -1 } } function Ef(e, t) { for (var n = e.suspendedLanes, r = e.pingedLanes, l = e.expirationTimes, i = e.pendingLanes; 0 < i;) { var a = 31 - Ie(i), u = 1 << a, o = l[a]; o === -1 ? (!(u & n) || u & r) && (l[a] = Cf(u, t)) : o <= t && (e.expiredLanes |= u), i &= ~u } } function qi(e) { return e = e.pendingLanes & -1073741825, e !== 0 ? e : e & 1073741824 ? 1073741824 : 0 } function Du() { var e = Or; return Or <<= 1, !(Or & 4194240) && (Or = 64), e } function ui(e) { for (var t = [], n = 0; 31 > n; n++)t.push(e); return t } function kr(e, t, n) { e.pendingLanes |= t, t !== 536870912 && (e.suspendedLanes = 0, e.pingedLanes = 0), e = e.eventTimes, t = 31 - Ie(t), e[t] = n } function _f(e, t) { var n = e.pendingLanes & ~t; e.pendingLanes = t, e.suspendedLanes = 0, e.pingedLanes = 0, e.expiredLanes &= t, e.mutableReadLanes &= t, e.entangledLanes &= t, t = e.entanglements; var r = e.eventTimes; for (e = e.expirationTimes; 0 < n;) { var l = 31 - Ie(n), i = 1 << l; t[l] = 0, r[l] = -1, e[l] = -1, n &= ~i } } function Us(e, t) { var n = e.entangledLanes |= t; for (e = e.entanglements; n;) { var r = 31 - Ie(n), l = 1 << r; l & t | e[r] & t && (e[r] |= t), n &= ~l } } var I = 0; function Ru(e) { return e &= -e, 1 < e ? 4 < e ? e & 268435455 ? 16 : 536870912 : 4 : 1 } var Ou, As, Lu, Iu, zu, Gi = !1, Ir = [], pt = null, ht = null, gt = null, sr = new Map, ar = new Map, ut = [], bf = "mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" "); function $a(e, t) { switch (e) { case "focusin": case "focusout": pt = null; break; case "dragenter": case "dragleave": ht = null; break; case "mouseover": case "mouseout": gt = null; break; case "pointerover": case "pointerout": sr.delete(t.pointerId); break; case "gotpointercapture": case "lostpointercapture": ar.delete(t.pointerId) } } function Ln(e, t, n, r, l, i) { return e === null || e.nativeEvent !== i ? (e = { blockedOn: t, domEventName: n, eventSystemFlags: r, nativeEvent: i, targetContainers: [l] }, t !== null && (t = Cr(t), t !== null && As(t)), e) : (e.eventSystemFlags |= r, t = e.targetContainers, l !== null && t.indexOf(l) === -1 && t.push(l), e) } function Mf(e, t, n, r, l) { switch (t) { case "focusin": return pt = Ln(pt, e, t, n, r, l), !0; case "dragenter": return ht = Ln(ht, e, t, n, r, l), !0; case "mouseover": return gt = Ln(gt, e, t, n, r, l), !0; case "pointerover": var i = l.pointerId; return sr.set(i, Ln(sr.get(i) || null, e, t, n, r, l)), !0; case "gotpointercapture": return i = l.pointerId, ar.set(i, Ln(ar.get(i) || null, e, t, n, r, l)), !0 }return !1 } function Fu(e) { var t = Lt(e.target); if (t !== null) { var n = qt(t); if (n !== null) { if (t = n.tag, t === 13) { if (t = Cu(n), t !== null) { e.blockedOn = t, zu(e.priority, function () { Lu(n) }); return } } else if (t === 3 && n.stateNode.current.memoizedState.isDehydrated) { e.blockedOn = n.tag === 3 ? n.stateNode.containerInfo : null; return } } } e.blockedOn = null } function Gr(e) { if (e.blockedOn !== null) return !1; for (var t = e.targetContainers; 0 < t.length;) { var n = Ki(e.domEventName, e.eventSystemFlags, t[0], e.nativeEvent); if (n === null) { n = e.nativeEvent; var r = new n.constructor(n.type, n); Hi = r, n.target.dispatchEvent(r), Hi = null } else return t = Cr(n), t !== null && As(t), e.blockedOn = n, !1; t.shift() } return !0 } function Ua(e, t, n) { Gr(e) && n.delete(t) } function Tf() { Gi = !1, pt !== null && Gr(pt) && (pt = null), ht !== null && Gr(ht) && (ht = null), gt !== null && Gr(gt) && (gt = null), sr.forEach(Ua), ar.forEach(Ua) } function In(e, t) { e.blockedOn === t && (e.blockedOn = null, Gi || (Gi = !0, Se.unstable_scheduleCallback(Se.unstable_NormalPriority, Tf))) } function or(e) { function t(l) { return In(l, e) } if (0 < Ir.length) { In(Ir[0], e); for (var n = 1; n < Ir.length; n++) { var r = Ir[n]; r.blockedOn === e && (r.blockedOn = null) } } for (pt !== null && In(pt, e), ht !== null && In(ht, e), gt !== null && In(gt, e), sr.forEach(t), ar.forEach(t), n = 0; n < ut.length; n++)r = ut[n], r.blockedOn === e && (r.blockedOn = null); for (; 0 < ut.length && (n = ut[0], n.blockedOn === null);)Fu(n), n.blockedOn === null && ut.shift() } var mn = rt.ReactCurrentBatchConfig, cl = !0; function Df(e, t, n, r) { var l = I, i = mn.transition; mn.transition = null; try { I = 1, Ws(e, t, n, r) } finally { I = l, mn.transition = i } } function Rf(e, t, n, r) { var l = I, i = mn.transition; mn.transition = null; try { I = 4, Ws(e, t, n, r) } finally { I = l, mn.transition = i } } function Ws(e, t, n, r) { if (cl) { var l = Ki(e, t, n, r); if (l === null) xi(e, t, r, dl, n), $a(e, r); else if (Mf(l, e, t, n, r)) r.stopPropagation(); else if ($a(e, r), t & 4 && -1 < bf.indexOf(e)) { for (; l !== null;) { var i = Cr(l); if (i !== null && Ou(i), i = Ki(e, t, n, r), i === null && xi(e, t, r, dl, n), i === l) break; l = i } l !== null && r.stopPropagation() } else xi(e, t, r, null, n) } } var dl = null; function Ki(e, t, n, r) { if (dl = null, e = Fs(r), e = Lt(e), e !== null) if (t = qt(e), t === null) e = null; else if (n = t.tag, n === 13) { if (e = Cu(t), e !== null) return e; e = null } else if (n === 3) { if (t.stateNode.current.memoizedState.isDehydrated) return t.tag === 3 ? t.stateNode.containerInfo : null; e = null } else t !== e && (e = null); return dl = e, null } function $u(e) { switch (e) { case "cancel": case "click": case "close": case "contextmenu": case "copy": case "cut": case "auxclick": case "dblclick": case "dragend": case "dragstart": case "drop": case "focusin": case "focusout": case "input": case "invalid": case "keydown": case "keypress": case "keyup": case "mousedown": case "mouseup": case "paste": case "pause": case "play": case "pointercancel": case "pointerdown": case "pointerup": case "ratechange": case "reset": case "resize": case "seeked": case "submit": case "touchcancel": case "touchend": case "touchstart": case "volumechange": case "change": case "selectionchange": case "textInput": case "compositionstart": case "compositionend": case "compositionupdate": case "beforeblur": case "afterblur": case "beforeinput": case "blur": case "fullscreenchange": case "focus": case "hashchange": case "popstate": case "select": case "selectstart": return 1; case "drag": case "dragenter": case "dragexit": case "dragleave": case "dragover": case "mousemove": case "mouseout": case "mouseover": case "pointermove": case "pointerout": case "pointerover": case "scroll": case "toggle": case "touchmove": case "wheel": case "mouseenter": case "mouseleave": case "pointerenter": case "pointerleave": return 4; case "message": switch (wf()) { case $s: return 1; case Mu: return 4; case ol: case Nf: return 16; case Tu: return 536870912; default: return 16 }default: return 16 } } var dt = null, Bs = null, Kr = null; function Uu() { if (Kr) return Kr; var e, t = Bs, n = t.length, r, l = "value" in dt ? dt.value : dt.textContent, i = l.length; for (e = 0; e < n && t[e] === l[e]; e++); var a = n - e; for (r = 1; r <= a && t[n - r] === l[i - r]; r++); return Kr = l.slice(e, 1 < r ? 1 - r : void 0) } function Xr(e) { var t = e.keyCode; return "charCode" in e ? (e = e.charCode, e === 0 && t === 13 && (e = 13)) : e = t, e === 10 && (e = 13), 32 <= e || e === 13 ? e : 0 } function zr() { return !0 } function Aa() { return !1 } function Pe(e) { function t(n, r, l, i, a) { this._reactName = n, this._targetInst = l, this.type = r, this.nativeEvent = i, this.target = a, this.currentTarget = null; for (var u in e) e.hasOwnProperty(u) && (n = e[u], this[u] = n ? n(i) : i[u]); return this.isDefaultPrevented = (i.defaultPrevented != null ? i.defaultPrevented : i.returnValue === !1) ? zr : Aa, this.isPropagationStopped = Aa, this } return Q(t.prototype, { preventDefault: function () { this.defaultPrevented = !0; var n = this.nativeEvent; n && (n.preventDefault ? n.preventDefault() : typeof n.returnValue != "unknown" && (n.returnValue = !1), this.isDefaultPrevented = zr) }, stopPropagation: function () { var n = this.nativeEvent; n && (n.stopPropagation ? n.stopPropagation() : typeof n.cancelBubble != "unknown" && (n.cancelBubble = !0), this.isPropagationStopped = zr) }, persist: function () { }, isPersistent: zr }), t } var Cn = { eventPhase: 0, bubbles: 0, cancelable: 0, timeStamp: function (e) { return e.timeStamp || Date.now() }, defaultPrevented: 0, isTrusted: 0 }, Hs = Pe(Cn), Pr = Q({}, Cn, { view: 0, detail: 0 }), Of = Pe(Pr), ci, di, zn, Il = Q({}, Pr, { screenX: 0, screenY: 0, clientX: 0, clientY: 0, pageX: 0, pageY: 0, ctrlKey: 0, shiftKey: 0, altKey: 0, metaKey: 0, getModifierState: Vs, button: 0, buttons: 0, relatedTarget: function (e) { return e.relatedTarget === void 0 ? e.fromElement === e.srcElement ? e.toElement : e.fromElement : e.relatedTarget }, movementX: function (e) { return "movementX" in e ? e.movementX : (e !== zn && (zn && e.type === "mousemove" ? (ci = e.screenX - zn.screenX, di = e.screenY - zn.screenY) : di = ci = 0, zn = e), ci) }, movementY: function (e) { return "movementY" in e ? e.movementY : di } }), Wa = Pe(Il), Lf = Q({}, Il, { dataTransfer: 0 }), If = Pe(Lf), zf = Q({}, Pr, { relatedTarget: 0 }), fi = Pe(zf), Ff = Q({}, Cn, { animationName: 0, elapsedTime: 0, pseudoElement: 0 }), $f = Pe(Ff), Uf = Q({}, Cn, { clipboardData: function (e) { return "clipboardData" in e ? e.clipboardData : window.clipboardData } }), Af = Pe(Uf), Wf = Q({}, Cn, { data: 0 }), Ba = Pe(Wf), Bf = { Esc: "Escape", Spacebar: " ", Left: "ArrowLeft", Up: "ArrowUp", Right: "ArrowRight", Down: "ArrowDown", Del: "Delete", Win: "OS", Menu: "ContextMenu", Apps: "ContextMenu", Scroll: "ScrollLock", MozPrintableKey: "Unidentified" }, Hf = { 8: "Backspace", 9: "Tab", 12: "Clear", 13: "Enter", 16: "Shift", 17: "Control", 18: "Alt", 19: "Pause", 20: "CapsLock", 27: "Escape", 32: " ", 33: "PageUp", 34: "PageDown", 35: "End", 36: "Home", 37: "ArrowLeft", 38: "ArrowUp", 39: "ArrowRight", 40: "ArrowDown", 45: "Insert", 46: "Delete", 112: "F1", 113: "F2", 114: "F3", 115: "F4", 116: "F5", 117: "F6", 118: "F7", 119: "F8", 120: "F9", 121: "F10", 122: "F11", 123: "F12", 144: "NumLock", 145: "ScrollLock", 224: "Meta" }, Vf = { Alt: "altKey", Control: "ctrlKey", Meta: "metaKey", Shift: "shiftKey" }; function Qf(e) { var t = this.nativeEvent; return t.getModifierState ? t.getModifierState(e) : (e = Vf[e]) ? !!t[e] : !1 } function Vs() { return Qf } var Yf = Q({}, Pr, { key: function (e) { if (e.key) { var t = Bf[e.key] || e.key; if (t !== "Unidentified") return t } return e.type === "keypress" ? (e = Xr(e), e === 13 ? "Enter" : String.fromCharCode(e)) : e.type === "keydown" || e.type === "keyup" ? Hf[e.keyCode] || "Unidentified" : "" }, code: 0, location: 0, ctrlKey: 0, shiftKey: 0, altKey: 0, metaKey: 0, repeat: 0, locale: 0, getModifierState: Vs, charCode: function (e) { return e.type === "keypress" ? Xr(e) : 0 }, keyCode: function (e) { return e.type === "keydown" || e.type === "keyup" ? e.keyCode : 0 }, which: function (e) { return e.type === "keypress" ? Xr(e) : e.type === "keydown" || e.type === "keyup" ? e.keyCode : 0 } }), qf = Pe(Yf), Gf = Q({}, Il, { pointerId: 0, width: 0, height: 0, pressure: 0, tangentialPressure: 0, tiltX: 0, tiltY: 0, twist: 0, pointerType: 0, isPrimary: 0 }), Ha = Pe(Gf), Kf = Q({}, Pr, { touches: 0, targetTouches: 0, changedTouches: 0, altKey: 0, metaKey: 0, ctrlKey: 0, shiftKey: 0, getModifierState: Vs }), Xf = Pe(Kf), Jf = Q({}, Cn, { propertyName: 0, elapsedTime: 0, pseudoElement: 0 }), Zf = Pe(Jf), em = Q({}, Il, { deltaX: function (e) { return "deltaX" in e ? e.deltaX : "wheelDeltaX" in e ? -e.wheelDeltaX : 0 }, deltaY: function (e) { return "deltaY" in e ? e.deltaY : "wheelDeltaY" in e ? -e.wheelDeltaY : "wheelDelta" in e ? -e.wheelDelta : 0 }, deltaZ: 0, deltaMode: 0 }), tm = Pe(em), nm = [9, 13, 27, 32], Qs = Ze && "CompositionEvent" in window, Gn = null; Ze && "documentMode" in document && (Gn = document.documentMode); var rm = Ze && "TextEvent" in window && !Gn, Au = Ze && (!Qs || Gn && 8 < Gn && 11 >= Gn), Va = " ", Qa = !1; function Wu(e, t) { switch (e) { case "keyup": return nm.indexOf(t.keyCode) !== -1; case "keydown": return t.keyCode !== 229; case "keypress": case "mousedown": case "focusout": return !0; default: return !1 } } function Bu(e) { return e = e.detail, typeof e == "object" && "data" in e ? e.data : null } var Zt = !1; function lm(e, t) { switch (e) { case "compositionend": return Bu(t); case "keypress": return t.which !== 32 ? null : (Qa = !0, Va); case "textInput": return e = t.data, e === Va && Qa ? null : e; default: return null } } function im(e, t) { if (Zt) return e === "compositionend" || !Qs && Wu(e, t) ? (e = Uu(), Kr = Bs = dt = null, Zt = !1, e) : null; switch (e) { case "paste": return null; case "keypress": if (!(t.ctrlKey || t.altKey || t.metaKey) || t.ctrlKey && t.altKey) { if (t.char && 1 < t.char.length) return t.char; if (t.which) return String.fromCharCode(t.which) } return null; case "compositionend": return Au && t.locale !== "ko" ? null : t.data; default: return null } } var sm = { color: !0, date: !0, datetime: !0, "datetime-local": !0, email: !0, month: !0, number: !0, password: !0, range: !0, search: !0, tel: !0, text: !0, time: !0, url: !0, week: !0 }; function Ya(e) { var t = e && e.nodeName && e.nodeName.toLowerCase(); return t === "input" ? !!sm[e.type] : t === "textarea" } function Hu(e, t, n, r) { Nu(r), t = fl(t, "onChange"), 0 < t.length && (n = new Hs("onChange", "change", null, n, r), e.push({ event: n, listeners: t })) } var Kn = null, ur = null; function am(e) { tc(e, 0) } function zl(e) { var t = nn(e); if (pu(t)) return e } function om(e, t) { if (e === "change") return t } var Vu = !1; if (Ze) { var mi; if (Ze) { var pi = "oninput" in document; if (!pi) { var qa = document.createElement("div"); qa.setAttribute("oninput", "return;"), pi = typeof qa.oninput == "function" } mi = pi } else mi = !1; Vu = mi && (!document.documentMode || 9 < document.documentMode) } function Ga() { Kn && (Kn.detachEvent("onpropertychange", Qu), ur = Kn = null) } function Qu(e) { if (e.propertyName === "value" && zl(ur)) { var t = []; Hu(t, ur, e, Fs(e)), Pu(am, t) } } function um(e, t, n) { e === "focusin" ? (Ga(), Kn = t, ur = n, Kn.attachEvent("onpropertychange", Qu)) : e === "focusout" && Ga() } function cm(e) { if (e === "selectionchange" || e === "keyup" || e === "keydown") return zl(ur) } function dm(e, t) { if (e === "click") return zl(t) } function fm(e, t) { if (e === "input" || e === "change") return zl(t) } function mm(e, t) { return e === t && (e !== 0 || 1 / e === 1 / t) || e !== e && t !== t } var Fe = typeof Object.is == "function" ? Object.is : mm; function cr(e, t) { if (Fe(e, t)) return !0; if (typeof e != "object" || e === null || typeof t != "object" || t === null) return !1; var n = Object.keys(e), r = Object.keys(t); if (n.length !== r.length) return !1; for (r = 0; r < n.length; r++) { var l = n[r]; if (!Di.call(t, l) || !Fe(e[l], t[l])) return !1 } return !0 } function Ka(e) { for (; e && e.firstChild;)e = e.firstChild; return e } function Xa(e, t) { var n = Ka(e); e = 0; for (var r; n;) { if (n.nodeType === 3) { if (r = e + n.textContent.length, e <= t && r >= t) return { node: n, offset: t - e }; e = r } e: { for (; n;) { if (n.nextSibling) { n = n.nextSibling; break e } n = n.parentNode } n = void 0 } n = Ka(n) } } function Yu(e, t) { return e && t ? e === t ? !0 : e && e.nodeType === 3 ? !1 : t && t.nodeType === 3 ? Yu(e, t.parentNode) : "contains" in e ? e.contains(t) : e.compareDocumentPosition ? !!(e.compareDocumentPosition(t) & 16) : !1 : !1 } function qu() { for (var e = window, t = il(); t instanceof e.HTMLIFrameElement;) { try { var n = typeof t.contentWindow.location.href == "string" } catch { n = !1 } if (n) e = t.contentWindow; else break; t = il(e.document) } return t } function Ys(e) { var t = e && e.nodeName && e.nodeName.toLowerCase(); return t && (t === "input" && (e.type === "text" || e.type === "search" || e.type === "tel" || e.type === "url" || e.type === "password") || t === "textarea" || e.contentEditable === "true") } function pm(e) { var t = qu(), n = e.focusedElem, r = e.selectionRange; if (t !== n && n && n.ownerDocument && Yu(n.ownerDocument.documentElement, n)) { if (r !== null && Ys(n)) { if (t = r.start, e = r.end, e === void 0 && (e = t), "selectionStart" in n) n.selectionStart = t, n.selectionEnd = Math.min(e, n.value.length); else if (e = (t = n.ownerDocument || document) && t.defaultView || window, e.getSelection) { e = e.getSelection(); var l = n.textContent.length, i = Math.min(r.start, l); r = r.end === void 0 ? i : Math.min(r.end, l), !e.extend && i > r && (l = r, r = i, i = l), l = Xa(n, i); var a = Xa(n, r); l && a && (e.rangeCount !== 1 || e.anchorNode !== l.node || e.anchorOffset !== l.offset || e.focusNode !== a.node || e.focusOffset !== a.offset) && (t = t.createRange(), t.setStart(l.node, l.offset), e.removeAllRanges(), i > r ? (e.addRange(t), e.extend(a.node, a.offset)) : (t.setEnd(a.node, a.offset), e.addRange(t))) } } for (t = [], e = n; e = e.parentNode;)e.nodeType === 1 && t.push({ element: e, left: e.scrollLeft, top: e.scrollTop }); for (typeof n.focus == "function" && n.focus(), n = 0; n < t.length; n++)e = t[n], e.element.scrollLeft = e.left, e.element.scrollTop = e.top } } var hm = Ze && "documentMode" in document && 11 >= document.documentMode, en = null, Xi = null, Xn = null, Ji = !1; function Ja(e, t, n) { var r = n.window === n ? n.document : n.nodeType === 9 ? n : n.ownerDocument; Ji || en == null || en !== il(r) || (r = en, "selectionStart" in r && Ys(r) ? r = { start: r.selectionStart, end: r.selectionEnd } : (r = (r.ownerDocument && r.ownerDocument.defaultView || window).getSelection(), r = { anchorNode: r.anchorNode, anchorOffset: r.anchorOffset, focusNode: r.focusNode, focusOffset: r.focusOffset }), Xn && cr(Xn, r) || (Xn = r, r = fl(Xi, "onSelect"), 0 < r.length && (t = new Hs("onSelect", "select", null, t, n), e.push({ event: t, listeners: r }), t.target = en))) } function Fr(e, t) { var n = {}; return n[e.toLowerCase()] = t.toLowerCase(), n["Webkit" + e] = "webkit" + t, n["Moz" + e] = "moz" + t, n } var tn = { animationend: Fr("Animation", "AnimationEnd"), animationiteration: Fr("Animation", "AnimationIteration"), animationstart: Fr("Animation", "AnimationStart"), transitionend: Fr("Transition", "TransitionEnd") }, hi = {}, Gu = {}; Ze && (Gu = document.createElement("div").style, "AnimationEvent" in window || (delete tn.animationend.animation, delete tn.animationiteration.animation, delete tn.animationstart.animation), "TransitionEvent" in window || delete tn.transitionend.transition); function Fl(e) { if (hi[e]) return hi[e]; if (!tn[e]) return e; var t = tn[e], n; for (n in t) if (t.hasOwnProperty(n) && n in Gu) return hi[e] = t[n]; return e } var Ku = Fl("animationend"), Xu = Fl("animationiteration"), Ju = Fl("animationstart"), Zu = Fl("transitionend"), ec = new Map, Za = "abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" "); function Pt(e, t) { ec.set(e, t), Yt(t, [e]) } for (var gi = 0; gi < Za.length; gi++) { var yi = Za[gi], gm = yi.toLowerCase(), ym = yi[0].toUpperCase() + yi.slice(1); Pt(gm, "on" + ym) } Pt(Ku, "onAnimationEnd"); Pt(Xu, "onAnimationIteration"); Pt(Ju, "onAnimationStart"); Pt("dblclick", "onDoubleClick"); Pt("focusin", "onFocus"); Pt("focusout", "onBlur"); Pt(Zu, "onTransitionEnd"); gn("onMouseEnter", ["mouseout", "mouseover"]); gn("onMouseLeave", ["mouseout", "mouseover"]); gn("onPointerEnter", ["pointerout", "pointerover"]); gn("onPointerLeave", ["pointerout", "pointerover"]); Yt("onChange", "change click focusin focusout input keydown keyup selectionchange".split(" ")); Yt("onSelect", "focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")); Yt("onBeforeInput", ["compositionend", "keypress", "textInput", "paste"]); Yt("onCompositionEnd", "compositionend focusout keydown keypress keyup mousedown".split(" ")); Yt("onCompositionStart", "compositionstart focusout keydown keypress keyup mousedown".split(" ")); Yt("onCompositionUpdate", "compositionupdate focusout keydown keypress keyup mousedown".split(" ")); var Qn = "abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "), vm = new Set("cancel close invalid load scroll toggle".split(" ").concat(Qn)); function eo(e, t, n) { var r = e.type || "unknown-event"; e.currentTarget = n, gf(r, t, void 0, e), e.currentTarget = null } function tc(e, t) { t = (t & 4) !== 0; for (var n = 0; n < e.length; n++) { var r = e[n], l = r.event; r = r.listeners; e: { var i = void 0; if (t) for (var a = r.length - 1; 0 <= a; a--) { var u = r[a], o = u.instance, c = u.currentTarget; if (u = u.listener, o !== i && l.isPropagationStopped()) break e; eo(l, u, c), i = o } else for (a = 0; a < r.length; a++) { if (u = r[a], o = u.instance, c = u.currentTarget, u = u.listener, o !== i && l.isPropagationStopped()) break e; eo(l, u, c), i = o } } } if (al) throw e = Yi, al = !1, Yi = null, e } function F(e, t) { var n = t[rs]; n === void 0 && (n = t[rs] = new Set); var r = e + "__bubble"; n.has(r) || (nc(t, e, 2, !1), n.add(r)) } function vi(e, t, n) { var r = 0; t && (r |= 4), nc(n, e, r, t) } var $r = "_reactListening" + Math.random().toString(36).slice(2); function dr(e) { if (!e[$r]) { e[$r] = !0, uu.forEach(function (n) { n !== "selectionchange" && (vm.has(n) || vi(n, !1, e), vi(n, !0, e)) }); var t = e.nodeType === 9 ? e : e.ownerDocument; t === null || t[$r] || (t[$r] = !0, vi("selectionchange", !1, t)) } } function nc(e, t, n, r) { switch ($u(t)) { case 1: var l = Df; break; case 4: l = Rf; break; default: l = Ws }n = l.bind(null, t, n, e), l = void 0, !Qi || t !== "touchstart" && t !== "touchmove" && t !== "wheel" || (l = !0), r ? l !== void 0 ? e.addEventListener(t, n, { capture: !0, passive: l }) : e.addEventListener(t, n, !0) : l !== void 0 ? e.addEventListener(t, n, { passive: l }) : e.addEventListener(t, n, !1) } function xi(e, t, n, r, l) { var i = r; if (!(t & 1) && !(t & 2) && r !== null) e: for (; ;) { if (r === null) return; var a = r.tag; if (a === 3 || a === 4) { var u = r.stateNode.containerInfo; if (u === l || u.nodeType === 8 && u.parentNode === l) break; if (a === 4) for (a = r.return; a !== null;) { var o = a.tag; if ((o === 3 || o === 4) && (o = a.stateNode.containerInfo, o === l || o.nodeType === 8 && o.parentNode === l)) return; a = a.return } for (; u !== null;) { if (a = Lt(u), a === null) return; if (o = a.tag, o === 5 || o === 6) { r = i = a; continue e } u = u.parentNode } } r = r.return } Pu(function () { var c = i, g = Fs(n), m = []; e: { var h = ec.get(e); if (h !== void 0) { var w = Hs, y = e; switch (e) { case "keypress": if (Xr(n) === 0) break e; case "keydown": case "keyup": w = qf; break; case "focusin": y = "focus", w = fi; break; case "focusout": y = "blur", w = fi; break; case "beforeblur": case "afterblur": w = fi; break; case "click": if (n.button === 2) break e; case "auxclick": case "dblclick": case "mousedown": case "mousemove": case "mouseup": case "mouseout": case "mouseover": case "contextmenu": w = Wa; break; case "drag": case "dragend": case "dragenter": case "dragexit": case "dragleave": case "dragover": case "dragstart": case "drop": w = If; break; case "touchcancel": case "touchend": case "touchmove": case "touchstart": w = Xf; break; case Ku: case Xu: case Ju: w = $f; break; case Zu: w = Zf; break; case "scroll": w = Of; break; case "wheel": w = tm; break; case "copy": case "cut": case "paste": w = Af; break; case "gotpointercapture": case "lostpointercapture": case "pointercancel": case "pointerdown": case "pointermove": case "pointerout": case "pointerover": case "pointerup": w = Ha }var x = (t & 4) !== 0, k = !x && e === "scroll", f = x ? h !== null ? h + "Capture" : null : h; x = []; for (var d = c, p; d !== null;) { p = d; var v = p.stateNode; if (p.tag === 5 && v !== null && (p = v, f !== null && (v = ir(d, f), v != null && x.push(fr(d, v, p)))), k) break; d = d.return } 0 < x.length && (h = new w(h, y, null, n, g), m.push({ event: h, listeners: x })) } } if (!(t & 7)) { e: { if (h = e === "mouseover" || e === "pointerover", w = e === "mouseout" || e === "pointerout", h && n !== Hi && (y = n.relatedTarget || n.fromElement) && (Lt(y) || y[et])) break e; if ((w || h) && (h = g.window === g ? g : (h = g.ownerDocument) ? h.defaultView || h.parentWindow : window, w ? (y = n.relatedTarget || n.toElement, w = c, y = y ? Lt(y) : null, y !== null && (k = qt(y), y !== k || y.tag !== 5 && y.tag !== 6) && (y = null)) : (w = null, y = c), w !== y)) { if (x = Wa, v = "onMouseLeave", f = "onMouseEnter", d = "mouse", (e === "pointerout" || e === "pointerover") && (x = Ha, v = "onPointerLeave", f = "onPointerEnter", d = "pointer"), k = w == null ? h : nn(w), p = y == null ? h : nn(y), h = new x(v, d + "leave", w, n, g), h.target = k, h.relatedTarget = p, v = null, Lt(g) === c && (x = new x(f, d + "enter", y, n, g), x.target = p, x.relatedTarget = k, v = x), k = v, w && y) t: { for (x = w, f = y, d = 0, p = x; p; p = Gt(p))d++; for (p = 0, v = f; v; v = Gt(v))p++; for (; 0 < d - p;)x = Gt(x), d--; for (; 0 < p - d;)f = Gt(f), p--; for (; d--;) { if (x === f || f !== null && x === f.alternate) break t; x = Gt(x), f = Gt(f) } x = null } else x = null; w !== null && to(m, h, w, x, !1), y !== null && k !== null && to(m, k, y, x, !0) } } e: { if (h = c ? nn(c) : window, w = h.nodeName && h.nodeName.toLowerCase(), w === "select" || w === "input" && h.type === "file") var j = om; else if (Ya(h)) if (Vu) j = fm; else { j = cm; var P = um } else (w = h.nodeName) && w.toLowerCase() === "input" && (h.type === "checkbox" || h.type === "radio") && (j = dm); if (j && (j = j(e, c))) { Hu(m, j, n, g); break e } P && P(e, h, c), e === "focusout" && (P = h._wrapperState) && P.controlled && h.type === "number" && $i(h, "number", h.value) } switch (P = c ? nn(c) : window, e) { case "focusin": (Ya(P) || P.contentEditable === "true") && (en = P, Xi = c, Xn = null); break; case "focusout": Xn = Xi = en = null; break; case "mousedown": Ji = !0; break; case "contextmenu": case "mouseup": case "dragend": Ji = !1, Ja(m, n, g); break; case "selectionchange": if (hm) break; case "keydown": case "keyup": Ja(m, n, g) }var C; if (Qs) e: { switch (e) { case "compositionstart": var b = "onCompositionStart"; break e; case "compositionend": b = "onCompositionEnd"; break e; case "compositionupdate": b = "onCompositionUpdate"; break e }b = void 0 } else Zt ? Wu(e, n) && (b = "onCompositionEnd") : e === "keydown" && n.keyCode === 229 && (b = "onCompositionStart"); b && (Au && n.locale !== "ko" && (Zt || b !== "onCompositionStart" ? b === "onCompositionEnd" && Zt && (C = Uu()) : (dt = g, Bs = "value" in dt ? dt.value : dt.textContent, Zt = !0)), P = fl(c, b), 0 < P.length && (b = new Ba(b, e, null, n, g), m.push({ event: b, listeners: P }), C ? b.data = C : (C = Bu(n), C !== null && (b.data = C)))), (C = rm ? lm(e, n) : im(e, n)) && (c = fl(c, "onBeforeInput"), 0 < c.length && (g = new Ba("onBeforeInput", "beforeinput", null, n, g), m.push({ event: g, listeners: c }), g.data = C)) } tc(m, t) }) } function fr(e, t, n) { return { instance: e, listener: t, currentTarget: n } } function fl(e, t) { for (var n = t + "Capture", r = []; e !== null;) { var l = e, i = l.stateNode; l.tag === 5 && i !== null && (l = i, i = ir(e, n), i != null && r.unshift(fr(e, i, l)), i = ir(e, t), i != null && r.push(fr(e, i, l))), e = e.return } return r } function Gt(e) { if (e === null) return null; do e = e.return; while (e && e.tag !== 5); return e || null } function to(e, t, n, r, l) { for (var i = t._reactName, a = []; n !== null && n !== r;) { var u = n, o = u.alternate, c = u.stateNode; if (o !== null && o === r) break; u.tag === 5 && c !== null && (u = c, l ? (o = ir(n, i), o != null && a.unshift(fr(n, o, u))) : l || (o = ir(n, i), o != null && a.push(fr(n, o, u)))), n = n.return } a.length !== 0 && e.push({ event: t, listeners: a }) } var xm = /\r\n?/g, wm = /\u0000|\uFFFD/g; function no(e) {
    return (typeof e == "string" ? e : "" + e).replace(xm, `
`).replace(wm, "")
} function Ur(e, t, n) { if (t = no(t), no(e) !== t && n) throw Error(S(425)) } function ml() { } var Zi = null, es = null; function ts(e, t) { return e === "textarea" || e === "noscript" || typeof t.children == "string" || typeof t.children == "number" || typeof t.dangerouslySetInnerHTML == "object" && t.dangerouslySetInnerHTML !== null && t.dangerouslySetInnerHTML.__html != null } var ns = typeof setTimeout == "function" ? setTimeout : void 0, Nm = typeof clearTimeout == "function" ? clearTimeout : void 0, ro = typeof Promise == "function" ? Promise : void 0, jm = typeof queueMicrotask == "function" ? queueMicrotask : typeof ro < "u" ? function (e) { return ro.resolve(null).then(e).catch(Sm) } : ns; function Sm(e) { setTimeout(function () { throw e }) } function wi(e, t) { var n = t, r = 0; do { var l = n.nextSibling; if (e.removeChild(n), l && l.nodeType === 8) if (n = l.data, n === "/$") { if (r === 0) { e.removeChild(l), or(t); return } r-- } else n !== "$" && n !== "$?" && n !== "$!" || r++; n = l } while (n); or(t) } function yt(e) { for (; e != null; e = e.nextSibling) { var t = e.nodeType; if (t === 1 || t === 3) break; if (t === 8) { if (t = e.data, t === "$" || t === "$!" || t === "$?") break; if (t === "/$") return null } } return e } function lo(e) { e = e.previousSibling; for (var t = 0; e;) { if (e.nodeType === 8) { var n = e.data; if (n === "$" || n === "$!" || n === "$?") { if (t === 0) return e; t-- } else n === "/$" && t++ } e = e.previousSibling } return null } var En = Math.random().toString(36).slice(2), Be = "__reactFiber$" + En, mr = "__reactProps$" + En, et = "__reactContainer$" + En, rs = "__reactEvents$" + En, km = "__reactListeners$" + En, Pm = "__reactHandles$" + En; function Lt(e) { var t = e[Be]; if (t) return t; for (var n = e.parentNode; n;) { if (t = n[et] || n[Be]) { if (n = t.alternate, t.child !== null || n !== null && n.child !== null) for (e = lo(e); e !== null;) { if (n = e[Be]) return n; e = lo(e) } return t } e = n, n = e.parentNode } return null } function Cr(e) { return e = e[Be] || e[et], !e || e.tag !== 5 && e.tag !== 6 && e.tag !== 13 && e.tag !== 3 ? null : e } function nn(e) { if (e.tag === 5 || e.tag === 6) return e.stateNode; throw Error(S(33)) } function $l(e) { return e[mr] || null } var ls = [], rn = -1; function Ct(e) { return { current: e } } function $(e) { 0 > rn || (e.current = ls[rn], ls[rn] = null, rn--) } function z(e, t) { rn++, ls[rn] = e.current, e.current = t } var kt = {}, oe = Ct(kt), ge = Ct(!1), At = kt; function yn(e, t) { var n = e.type.contextTypes; if (!n) return kt; var r = e.stateNode; if (r && r.__reactInternalMemoizedUnmaskedChildContext === t) return r.__reactInternalMemoizedMaskedChildContext; var l = {}, i; for (i in n) l[i] = t[i]; return r && (e = e.stateNode, e.__reactInternalMemoizedUnmaskedChildContext = t, e.__reactInternalMemoizedMaskedChildContext = l), l } function ye(e) { return e = e.childContextTypes, e != null } function pl() { $(ge), $(oe) } function io(e, t, n) { if (oe.current !== kt) throw Error(S(168)); z(oe, t), z(ge, n) } function rc(e, t, n) { var r = e.stateNode; if (t = t.childContextTypes, typeof r.getChildContext != "function") return n; r = r.getChildContext(); for (var l in r) if (!(l in t)) throw Error(S(108, uf(e) || "Unknown", l)); return Q({}, n, r) } function hl(e) { return e = (e = e.stateNode) && e.__reactInternalMemoizedMergedChildContext || kt, At = oe.current, z(oe, e), z(ge, ge.current), !0 } function so(e, t, n) { var r = e.stateNode; if (!r) throw Error(S(169)); n ? (e = rc(e, t, At), r.__reactInternalMemoizedMergedChildContext = e, $(ge), $(oe), z(oe, e)) : $(ge), z(ge, n) } var Ge = null, Ul = !1, Ni = !1; function lc(e) { Ge === null ? Ge = [e] : Ge.push(e) } function Cm(e) { Ul = !0, lc(e) } function Et() { if (!Ni && Ge !== null) { Ni = !0; var e = 0, t = I; try { var n = Ge; for (I = 1; e < n.length; e++) { var r = n[e]; do r = r(!0); while (r !== null) } Ge = null, Ul = !1 } catch (l) { throw Ge !== null && (Ge = Ge.slice(e + 1)), bu($s, Et), l } finally { I = t, Ni = !1 } } return null } var ln = [], sn = 0, gl = null, yl = 0, Ce = [], Ee = 0, Wt = null, Ke = 1, Xe = ""; function Tt(e, t) { ln[sn++] = yl, ln[sn++] = gl, gl = e, yl = t } function ic(e, t, n) { Ce[Ee++] = Ke, Ce[Ee++] = Xe, Ce[Ee++] = Wt, Wt = e; var r = Ke; e = Xe; var l = 32 - Ie(r) - 1; r &= ~(1 << l), n += 1; var i = 32 - Ie(t) + l; if (30 < i) { var a = l - l % 5; i = (r & (1 << a) - 1).toString(32), r >>= a, l -= a, Ke = 1 << 32 - Ie(t) + l | n << l | r, Xe = i + e } else Ke = 1 << i | n << l | r, Xe = e } function qs(e) { e.return !== null && (Tt(e, 1), ic(e, 1, 0)) } function Gs(e) { for (; e === gl;)gl = ln[--sn], ln[sn] = null, yl = ln[--sn], ln[sn] = null; for (; e === Wt;)Wt = Ce[--Ee], Ce[Ee] = null, Xe = Ce[--Ee], Ce[Ee] = null, Ke = Ce[--Ee], Ce[Ee] = null } var je = null, Ne = null, A = !1, Le = null; function sc(e, t) { var n = _e(5, null, null, 0); n.elementType = "DELETED", n.stateNode = t, n.return = e, t = e.deletions, t === null ? (e.deletions = [n], e.flags |= 16) : t.push(n) } function ao(e, t) { switch (e.tag) { case 5: var n = e.type; return t = t.nodeType !== 1 || n.toLowerCase() !== t.nodeName.toLowerCase() ? null : t, t !== null ? (e.stateNode = t, je = e, Ne = yt(t.firstChild), !0) : !1; case 6: return t = e.pendingProps === "" || t.nodeType !== 3 ? null : t, t !== null ? (e.stateNode = t, je = e, Ne = null, !0) : !1; case 13: return t = t.nodeType !== 8 ? null : t, t !== null ? (n = Wt !== null ? { id: Ke, overflow: Xe } : null, e.memoizedState = { dehydrated: t, treeContext: n, retryLane: 1073741824 }, n = _e(18, null, null, 0), n.stateNode = t, n.return = e, e.child = n, je = e, Ne = null, !0) : !1; default: return !1 } } function is(e) { return (e.mode & 1) !== 0 && (e.flags & 128) === 0 } function ss(e) { if (A) { var t = Ne; if (t) { var n = t; if (!ao(e, t)) { if (is(e)) throw Error(S(418)); t = yt(n.nextSibling); var r = je; t && ao(e, t) ? sc(r, n) : (e.flags = e.flags & -4097 | 2, A = !1, je = e) } } else { if (is(e)) throw Error(S(418)); e.flags = e.flags & -4097 | 2, A = !1, je = e } } } function oo(e) { for (e = e.return; e !== null && e.tag !== 5 && e.tag !== 3 && e.tag !== 13;)e = e.return; je = e } function Ar(e) { if (e !== je) return !1; if (!A) return oo(e), A = !0, !1; var t; if ((t = e.tag !== 3) && !(t = e.tag !== 5) && (t = e.type, t = t !== "head" && t !== "body" && !ts(e.type, e.memoizedProps)), t && (t = Ne)) { if (is(e)) throw ac(), Error(S(418)); for (; t;)sc(e, t), t = yt(t.nextSibling) } if (oo(e), e.tag === 13) { if (e = e.memoizedState, e = e !== null ? e.dehydrated : null, !e) throw Error(S(317)); e: { for (e = e.nextSibling, t = 0; e;) { if (e.nodeType === 8) { var n = e.data; if (n === "/$") { if (t === 0) { Ne = yt(e.nextSibling); break e } t-- } else n !== "$" && n !== "$!" && n !== "$?" || t++ } e = e.nextSibling } Ne = null } } else Ne = je ? yt(e.stateNode.nextSibling) : null; return !0 } function ac() { for (var e = Ne; e;)e = yt(e.nextSibling) } function vn() { Ne = je = null, A = !1 } function Ks(e) { Le === null ? Le = [e] : Le.push(e) } var Em = rt.ReactCurrentBatchConfig; function Fn(e, t, n) { if (e = n.ref, e !== null && typeof e != "function" && typeof e != "object") { if (n._owner) { if (n = n._owner, n) { if (n.tag !== 1) throw Error(S(309)); var r = n.stateNode } if (!r) throw Error(S(147, e)); var l = r, i = "" + e; return t !== null && t.ref !== null && typeof t.ref == "function" && t.ref._stringRef === i ? t.ref : (t = function (a) { var u = l.refs; a === null ? delete u[i] : u[i] = a }, t._stringRef = i, t) } if (typeof e != "string") throw Error(S(284)); if (!n._owner) throw Error(S(290, e)) } return e } function Wr(e, t) { throw e = Object.prototype.toString.call(t), Error(S(31, e === "[object Object]" ? "object with keys {" + Object.keys(t).join(", ") + "}" : e)) } function uo(e) { var t = e._init; return t(e._payload) } function oc(e) { function t(f, d) { if (e) { var p = f.deletions; p === null ? (f.deletions = [d], f.flags |= 16) : p.push(d) } } function n(f, d) { if (!e) return null; for (; d !== null;)t(f, d), d = d.sibling; return null } function r(f, d) { for (f = new Map; d !== null;)d.key !== null ? f.set(d.key, d) : f.set(d.index, d), d = d.sibling; return f } function l(f, d) { return f = Nt(f, d), f.index = 0, f.sibling = null, f } function i(f, d, p) { return f.index = p, e ? (p = f.alternate, p !== null ? (p = p.index, p < d ? (f.flags |= 2, d) : p) : (f.flags |= 2, d)) : (f.flags |= 1048576, d) } function a(f) { return e && f.alternate === null && (f.flags |= 2), f } function u(f, d, p, v) { return d === null || d.tag !== 6 ? (d = _i(p, f.mode, v), d.return = f, d) : (d = l(d, p), d.return = f, d) } function o(f, d, p, v) { var j = p.type; return j === Jt ? g(f, d, p.props.children, v, p.key) : d !== null && (d.elementType === j || typeof j == "object" && j !== null && j.$$typeof === at && uo(j) === d.type) ? (v = l(d, p.props), v.ref = Fn(f, d, p), v.return = f, v) : (v = ll(p.type, p.key, p.props, null, f.mode, v), v.ref = Fn(f, d, p), v.return = f, v) } function c(f, d, p, v) { return d === null || d.tag !== 4 || d.stateNode.containerInfo !== p.containerInfo || d.stateNode.implementation !== p.implementation ? (d = bi(p, f.mode, v), d.return = f, d) : (d = l(d, p.children || []), d.return = f, d) } function g(f, d, p, v, j) { return d === null || d.tag !== 7 ? (d = $t(p, f.mode, v, j), d.return = f, d) : (d = l(d, p), d.return = f, d) } function m(f, d, p) { if (typeof d == "string" && d !== "" || typeof d == "number") return d = _i("" + d, f.mode, p), d.return = f, d; if (typeof d == "object" && d !== null) { switch (d.$$typeof) { case Tr: return p = ll(d.type, d.key, d.props, null, f.mode, p), p.ref = Fn(f, null, d), p.return = f, p; case Xt: return d = bi(d, f.mode, p), d.return = f, d; case at: var v = d._init; return m(f, v(d._payload), p) }if (Hn(d) || Rn(d)) return d = $t(d, f.mode, p, null), d.return = f, d; Wr(f, d) } return null } function h(f, d, p, v) { var j = d !== null ? d.key : null; if (typeof p == "string" && p !== "" || typeof p == "number") return j !== null ? null : u(f, d, "" + p, v); if (typeof p == "object" && p !== null) { switch (p.$$typeof) { case Tr: return p.key === j ? o(f, d, p, v) : null; case Xt: return p.key === j ? c(f, d, p, v) : null; case at: return j = p._init, h(f, d, j(p._payload), v) }if (Hn(p) || Rn(p)) return j !== null ? null : g(f, d, p, v, null); Wr(f, p) } return null } function w(f, d, p, v, j) { if (typeof v == "string" && v !== "" || typeof v == "number") return f = f.get(p) || null, u(d, f, "" + v, j); if (typeof v == "object" && v !== null) { switch (v.$$typeof) { case Tr: return f = f.get(v.key === null ? p : v.key) || null, o(d, f, v, j); case Xt: return f = f.get(v.key === null ? p : v.key) || null, c(d, f, v, j); case at: var P = v._init; return w(f, d, p, P(v._payload), j) }if (Hn(v) || Rn(v)) return f = f.get(p) || null, g(d, f, v, j, null); Wr(d, v) } return null } function y(f, d, p, v) { for (var j = null, P = null, C = d, b = d = 0, W = null; C !== null && b < p.length; b++) { C.index > b ? (W = C, C = null) : W = C.sibling; var T = h(f, C, p[b], v); if (T === null) { C === null && (C = W); break } e && C && T.alternate === null && t(f, C), d = i(T, d, b), P === null ? j = T : P.sibling = T, P = T, C = W } if (b === p.length) return n(f, C), A && Tt(f, b), j; if (C === null) { for (; b < p.length; b++)C = m(f, p[b], v), C !== null && (d = i(C, d, b), P === null ? j = C : P.sibling = C, P = C); return A && Tt(f, b), j } for (C = r(f, C); b < p.length; b++)W = w(C, f, b, p[b], v), W !== null && (e && W.alternate !== null && C.delete(W.key === null ? b : W.key), d = i(W, d, b), P === null ? j = W : P.sibling = W, P = W); return e && C.forEach(function (xe) { return t(f, xe) }), A && Tt(f, b), j } function x(f, d, p, v) { var j = Rn(p); if (typeof j != "function") throw Error(S(150)); if (p = j.call(p), p == null) throw Error(S(151)); for (var P = j = null, C = d, b = d = 0, W = null, T = p.next(); C !== null && !T.done; b++, T = p.next()) { C.index > b ? (W = C, C = null) : W = C.sibling; var xe = h(f, C, T.value, v); if (xe === null) { C === null && (C = W); break } e && C && xe.alternate === null && t(f, C), d = i(xe, d, b), P === null ? j = xe : P.sibling = xe, P = xe, C = W } if (T.done) return n(f, C), A && Tt(f, b), j; if (C === null) { for (; !T.done; b++, T = p.next())T = m(f, T.value, v), T !== null && (d = i(T, d, b), P === null ? j = T : P.sibling = T, P = T); return A && Tt(f, b), j } for (C = r(f, C); !T.done; b++, T = p.next())T = w(C, f, b, T.value, v), T !== null && (e && T.alternate !== null && C.delete(T.key === null ? b : T.key), d = i(T, d, b), P === null ? j = T : P.sibling = T, P = T); return e && C.forEach(function (Tn) { return t(f, Tn) }), A && Tt(f, b), j } function k(f, d, p, v) { if (typeof p == "object" && p !== null && p.type === Jt && p.key === null && (p = p.props.children), typeof p == "object" && p !== null) { switch (p.$$typeof) { case Tr: e: { for (var j = p.key, P = d; P !== null;) { if (P.key === j) { if (j = p.type, j === Jt) { if (P.tag === 7) { n(f, P.sibling), d = l(P, p.props.children), d.return = f, f = d; break e } } else if (P.elementType === j || typeof j == "object" && j !== null && j.$$typeof === at && uo(j) === P.type) { n(f, P.sibling), d = l(P, p.props), d.ref = Fn(f, P, p), d.return = f, f = d; break e } n(f, P); break } else t(f, P); P = P.sibling } p.type === Jt ? (d = $t(p.props.children, f.mode, v, p.key), d.return = f, f = d) : (v = ll(p.type, p.key, p.props, null, f.mode, v), v.ref = Fn(f, d, p), v.return = f, f = v) } return a(f); case Xt: e: { for (P = p.key; d !== null;) { if (d.key === P) if (d.tag === 4 && d.stateNode.containerInfo === p.containerInfo && d.stateNode.implementation === p.implementation) { n(f, d.sibling), d = l(d, p.children || []), d.return = f, f = d; break e } else { n(f, d); break } else t(f, d); d = d.sibling } d = bi(p, f.mode, v), d.return = f, f = d } return a(f); case at: return P = p._init, k(f, d, P(p._payload), v) }if (Hn(p)) return y(f, d, p, v); if (Rn(p)) return x(f, d, p, v); Wr(f, p) } return typeof p == "string" && p !== "" || typeof p == "number" ? (p = "" + p, d !== null && d.tag === 6 ? (n(f, d.sibling), d = l(d, p), d.return = f, f = d) : (n(f, d), d = _i(p, f.mode, v), d.return = f, f = d), a(f)) : n(f, d) } return k } var xn = oc(!0), uc = oc(!1), vl = Ct(null), xl = null, an = null, Xs = null; function Js() { Xs = an = xl = null } function Zs(e) { var t = vl.current; $(vl), e._currentValue = t } function as(e, t, n) { for (; e !== null;) { var r = e.alternate; if ((e.childLanes & t) !== t ? (e.childLanes |= t, r !== null && (r.childLanes |= t)) : r !== null && (r.childLanes & t) !== t && (r.childLanes |= t), e === n) break; e = e.return } } function pn(e, t) { xl = e, Xs = an = null, e = e.dependencies, e !== null && e.firstContext !== null && (e.lanes & t && (he = !0), e.firstContext = null) } function Me(e) { var t = e._currentValue; if (Xs !== e) if (e = { context: e, memoizedValue: t, next: null }, an === null) { if (xl === null) throw Error(S(308)); an = e, xl.dependencies = { lanes: 0, firstContext: e } } else an = an.next = e; return t } var It = null; function ea(e) { It === null ? It = [e] : It.push(e) } function cc(e, t, n, r) { var l = t.interleaved; return l === null ? (n.next = n, ea(t)) : (n.next = l.next, l.next = n), t.interleaved = n, tt(e, r) } function tt(e, t) { e.lanes |= t; var n = e.alternate; for (n !== null && (n.lanes |= t), n = e, e = e.return; e !== null;)e.childLanes |= t, n = e.alternate, n !== null && (n.childLanes |= t), n = e, e = e.return; return n.tag === 3 ? n.stateNode : null } var ot = !1; function ta(e) { e.updateQueue = { baseState: e.memoizedState, firstBaseUpdate: null, lastBaseUpdate: null, shared: { pending: null, interleaved: null, lanes: 0 }, effects: null } } function dc(e, t) { e = e.updateQueue, t.updateQueue === e && (t.updateQueue = { baseState: e.baseState, firstBaseUpdate: e.firstBaseUpdate, lastBaseUpdate: e.lastBaseUpdate, shared: e.shared, effects: e.effects }) } function Je(e, t) { return { eventTime: e, lane: t, tag: 0, payload: null, callback: null, next: null } } function vt(e, t, n) { var r = e.updateQueue; if (r === null) return null; if (r = r.shared, O & 2) { var l = r.pending; return l === null ? t.next = t : (t.next = l.next, l.next = t), r.pending = t, tt(e, n) } return l = r.interleaved, l === null ? (t.next = t, ea(r)) : (t.next = l.next, l.next = t), r.interleaved = t, tt(e, n) } function Jr(e, t, n) { if (t = t.updateQueue, t !== null && (t = t.shared, (n & 4194240) !== 0)) { var r = t.lanes; r &= e.pendingLanes, n |= r, t.lanes = n, Us(e, n) } } function co(e, t) { var n = e.updateQueue, r = e.alternate; if (r !== null && (r = r.updateQueue, n === r)) { var l = null, i = null; if (n = n.firstBaseUpdate, n !== null) { do { var a = { eventTime: n.eventTime, lane: n.lane, tag: n.tag, payload: n.payload, callback: n.callback, next: null }; i === null ? l = i = a : i = i.next = a, n = n.next } while (n !== null); i === null ? l = i = t : i = i.next = t } else l = i = t; n = { baseState: r.baseState, firstBaseUpdate: l, lastBaseUpdate: i, shared: r.shared, effects: r.effects }, e.updateQueue = n; return } e = n.lastBaseUpdate, e === null ? n.firstBaseUpdate = t : e.next = t, n.lastBaseUpdate = t } function wl(e, t, n, r) { var l = e.updateQueue; ot = !1; var i = l.firstBaseUpdate, a = l.lastBaseUpdate, u = l.shared.pending; if (u !== null) { l.shared.pending = null; var o = u, c = o.next; o.next = null, a === null ? i = c : a.next = c, a = o; var g = e.alternate; g !== null && (g = g.updateQueue, u = g.lastBaseUpdate, u !== a && (u === null ? g.firstBaseUpdate = c : u.next = c, g.lastBaseUpdate = o)) } if (i !== null) { var m = l.baseState; a = 0, g = c = o = null, u = i; do { var h = u.lane, w = u.eventTime; if ((r & h) === h) { g !== null && (g = g.next = { eventTime: w, lane: 0, tag: u.tag, payload: u.payload, callback: u.callback, next: null }); e: { var y = e, x = u; switch (h = t, w = n, x.tag) { case 1: if (y = x.payload, typeof y == "function") { m = y.call(w, m, h); break e } m = y; break e; case 3: y.flags = y.flags & -65537 | 128; case 0: if (y = x.payload, h = typeof y == "function" ? y.call(w, m, h) : y, h == null) break e; m = Q({}, m, h); break e; case 2: ot = !0 } } u.callback !== null && u.lane !== 0 && (e.flags |= 64, h = l.effects, h === null ? l.effects = [u] : h.push(u)) } else w = { eventTime: w, lane: h, tag: u.tag, payload: u.payload, callback: u.callback, next: null }, g === null ? (c = g = w, o = m) : g = g.next = w, a |= h; if (u = u.next, u === null) { if (u = l.shared.pending, u === null) break; h = u, u = h.next, h.next = null, l.lastBaseUpdate = h, l.shared.pending = null } } while (!0); if (g === null && (o = m), l.baseState = o, l.firstBaseUpdate = c, l.lastBaseUpdate = g, t = l.shared.interleaved, t !== null) { l = t; do a |= l.lane, l = l.next; while (l !== t) } else i === null && (l.shared.lanes = 0); Ht |= a, e.lanes = a, e.memoizedState = m } } function fo(e, t, n) { if (e = t.effects, t.effects = null, e !== null) for (t = 0; t < e.length; t++) { var r = e[t], l = r.callback; if (l !== null) { if (r.callback = null, r = n, typeof l != "function") throw Error(S(191, l)); l.call(r) } } } var Er = {}, Qe = Ct(Er), pr = Ct(Er), hr = Ct(Er); function zt(e) { if (e === Er) throw Error(S(174)); return e } function na(e, t) { switch (z(hr, t), z(pr, e), z(Qe, Er), e = t.nodeType, e) { case 9: case 11: t = (t = t.documentElement) ? t.namespaceURI : Ai(null, ""); break; default: e = e === 8 ? t.parentNode : t, t = e.namespaceURI || null, e = e.tagName, t = Ai(t, e) }$(Qe), z(Qe, t) } function wn() { $(Qe), $(pr), $(hr) } function fc(e) { zt(hr.current); var t = zt(Qe.current), n = Ai(t, e.type); t !== n && (z(pr, e), z(Qe, n)) } function ra(e) { pr.current === e && ($(Qe), $(pr)) } var B = Ct(0); function Nl(e) { for (var t = e; t !== null;) { if (t.tag === 13) { var n = t.memoizedState; if (n !== null && (n = n.dehydrated, n === null || n.data === "$?" || n.data === "$!")) return t } else if (t.tag === 19 && t.memoizedProps.revealOrder !== void 0) { if (t.flags & 128) return t } else if (t.child !== null) { t.child.return = t, t = t.child; continue } if (t === e) break; for (; t.sibling === null;) { if (t.return === null || t.return === e) return null; t = t.return } t.sibling.return = t.return, t = t.sibling } return null } var ji = []; function la() { for (var e = 0; e < ji.length; e++)ji[e]._workInProgressVersionPrimary = null; ji.length = 0 } var Zr = rt.ReactCurrentDispatcher, Si = rt.ReactCurrentBatchConfig, Bt = 0, H = null, X = null, ee = null, jl = !1, Jn = !1, gr = 0, _m = 0; function ie() { throw Error(S(321)) } function ia(e, t) { if (t === null) return !1; for (var n = 0; n < t.length && n < e.length; n++)if (!Fe(e[n], t[n])) return !1; return !0 } function sa(e, t, n, r, l, i) { if (Bt = i, H = t, t.memoizedState = null, t.updateQueue = null, t.lanes = 0, Zr.current = e === null || e.memoizedState === null ? Dm : Rm, e = n(r, l), Jn) { i = 0; do { if (Jn = !1, gr = 0, 25 <= i) throw Error(S(301)); i += 1, ee = X = null, t.updateQueue = null, Zr.current = Om, e = n(r, l) } while (Jn) } if (Zr.current = Sl, t = X !== null && X.next !== null, Bt = 0, ee = X = H = null, jl = !1, t) throw Error(S(300)); return e } function aa() { var e = gr !== 0; return gr = 0, e } function We() { var e = { memoizedState: null, baseState: null, baseQueue: null, queue: null, next: null }; return ee === null ? H.memoizedState = ee = e : ee = ee.next = e, ee } function Te() { if (X === null) { var e = H.alternate; e = e !== null ? e.memoizedState : null } else e = X.next; var t = ee === null ? H.memoizedState : ee.next; if (t !== null) ee = t, X = e; else { if (e === null) throw Error(S(310)); X = e, e = { memoizedState: X.memoizedState, baseState: X.baseState, baseQueue: X.baseQueue, queue: X.queue, next: null }, ee === null ? H.memoizedState = ee = e : ee = ee.next = e } return ee } function yr(e, t) { return typeof t == "function" ? t(e) : t } function ki(e) { var t = Te(), n = t.queue; if (n === null) throw Error(S(311)); n.lastRenderedReducer = e; var r = X, l = r.baseQueue, i = n.pending; if (i !== null) { if (l !== null) { var a = l.next; l.next = i.next, i.next = a } r.baseQueue = l = i, n.pending = null } if (l !== null) { i = l.next, r = r.baseState; var u = a = null, o = null, c = i; do { var g = c.lane; if ((Bt & g) === g) o !== null && (o = o.next = { lane: 0, action: c.action, hasEagerState: c.hasEagerState, eagerState: c.eagerState, next: null }), r = c.hasEagerState ? c.eagerState : e(r, c.action); else { var m = { lane: g, action: c.action, hasEagerState: c.hasEagerState, eagerState: c.eagerState, next: null }; o === null ? (u = o = m, a = r) : o = o.next = m, H.lanes |= g, Ht |= g } c = c.next } while (c !== null && c !== i); o === null ? a = r : o.next = u, Fe(r, t.memoizedState) || (he = !0), t.memoizedState = r, t.baseState = a, t.baseQueue = o, n.lastRenderedState = r } if (e = n.interleaved, e !== null) { l = e; do i = l.lane, H.lanes |= i, Ht |= i, l = l.next; while (l !== e) } else l === null && (n.lanes = 0); return [t.memoizedState, n.dispatch] } function Pi(e) { var t = Te(), n = t.queue; if (n === null) throw Error(S(311)); n.lastRenderedReducer = e; var r = n.dispatch, l = n.pending, i = t.memoizedState; if (l !== null) { n.pending = null; var a = l = l.next; do i = e(i, a.action), a = a.next; while (a !== l); Fe(i, t.memoizedState) || (he = !0), t.memoizedState = i, t.baseQueue === null && (t.baseState = i), n.lastRenderedState = i } return [i, r] } function mc() { } function pc(e, t) { var n = H, r = Te(), l = t(), i = !Fe(r.memoizedState, l); if (i && (r.memoizedState = l, he = !0), r = r.queue, oa(yc.bind(null, n, r, e), [e]), r.getSnapshot !== t || i || ee !== null && ee.memoizedState.tag & 1) { if (n.flags |= 2048, vr(9, gc.bind(null, n, r, l, t), void 0, null), te === null) throw Error(S(349)); Bt & 30 || hc(n, t, l) } return l } function hc(e, t, n) { e.flags |= 16384, e = { getSnapshot: t, value: n }, t = H.updateQueue, t === null ? (t = { lastEffect: null, stores: null }, H.updateQueue = t, t.stores = [e]) : (n = t.stores, n === null ? t.stores = [e] : n.push(e)) } function gc(e, t, n, r) { t.value = n, t.getSnapshot = r, vc(t) && xc(e) } function yc(e, t, n) { return n(function () { vc(t) && xc(e) }) } function vc(e) { var t = e.getSnapshot; e = e.value; try { var n = t(); return !Fe(e, n) } catch { return !0 } } function xc(e) { var t = tt(e, 1); t !== null && ze(t, e, 1, -1) } function mo(e) { var t = We(); return typeof e == "function" && (e = e()), t.memoizedState = t.baseState = e, e = { pending: null, interleaved: null, lanes: 0, dispatch: null, lastRenderedReducer: yr, lastRenderedState: e }, t.queue = e, e = e.dispatch = Tm.bind(null, H, e), [t.memoizedState, e] } function vr(e, t, n, r) { return e = { tag: e, create: t, destroy: n, deps: r, next: null }, t = H.updateQueue, t === null ? (t = { lastEffect: null, stores: null }, H.updateQueue = t, t.lastEffect = e.next = e) : (n = t.lastEffect, n === null ? t.lastEffect = e.next = e : (r = n.next, n.next = e, e.next = r, t.lastEffect = e)), e } function wc() { return Te().memoizedState } function el(e, t, n, r) { var l = We(); H.flags |= e, l.memoizedState = vr(1 | t, n, void 0, r === void 0 ? null : r) } function Al(e, t, n, r) { var l = Te(); r = r === void 0 ? null : r; var i = void 0; if (X !== null) { var a = X.memoizedState; if (i = a.destroy, r !== null && ia(r, a.deps)) { l.memoizedState = vr(t, n, i, r); return } } H.flags |= e, l.memoizedState = vr(1 | t, n, i, r) } function po(e, t) { return el(8390656, 8, e, t) } function oa(e, t) { return Al(2048, 8, e, t) } function Nc(e, t) { return Al(4, 2, e, t) } function jc(e, t) { return Al(4, 4, e, t) } function Sc(e, t) { if (typeof t == "function") return e = e(), t(e), function () { t(null) }; if (t != null) return e = e(), t.current = e, function () { t.current = null } } function kc(e, t, n) { return n = n != null ? n.concat([e]) : null, Al(4, 4, Sc.bind(null, t, e), n) } function ua() { } function Pc(e, t) { var n = Te(); t = t === void 0 ? null : t; var r = n.memoizedState; return r !== null && t !== null && ia(t, r[1]) ? r[0] : (n.memoizedState = [e, t], e) } function Cc(e, t) { var n = Te(); t = t === void 0 ? null : t; var r = n.memoizedState; return r !== null && t !== null && ia(t, r[1]) ? r[0] : (e = e(), n.memoizedState = [e, t], e) } function Ec(e, t, n) { return Bt & 21 ? (Fe(n, t) || (n = Du(), H.lanes |= n, Ht |= n, e.baseState = !0), t) : (e.baseState && (e.baseState = !1, he = !0), e.memoizedState = n) } function bm(e, t) { var n = I; I = n !== 0 && 4 > n ? n : 4, e(!0); var r = Si.transition; Si.transition = {}; try { e(!1), t() } finally { I = n, Si.transition = r } } function _c() { return Te().memoizedState } function Mm(e, t, n) { var r = wt(e); if (n = { lane: r, action: n, hasEagerState: !1, eagerState: null, next: null }, bc(e)) Mc(t, n); else if (n = cc(e, t, n, r), n !== null) { var l = ce(); ze(n, e, r, l), Tc(n, t, r) } } function Tm(e, t, n) { var r = wt(e), l = { lane: r, action: n, hasEagerState: !1, eagerState: null, next: null }; if (bc(e)) Mc(t, l); else { var i = e.alternate; if (e.lanes === 0 && (i === null || i.lanes === 0) && (i = t.lastRenderedReducer, i !== null)) try { var a = t.lastRenderedState, u = i(a, n); if (l.hasEagerState = !0, l.eagerState = u, Fe(u, a)) { var o = t.interleaved; o === null ? (l.next = l, ea(t)) : (l.next = o.next, o.next = l), t.interleaved = l; return } } catch { } finally { } n = cc(e, t, l, r), n !== null && (l = ce(), ze(n, e, r, l), Tc(n, t, r)) } } function bc(e) { var t = e.alternate; return e === H || t !== null && t === H } function Mc(e, t) { Jn = jl = !0; var n = e.pending; n === null ? t.next = t : (t.next = n.next, n.next = t), e.pending = t } function Tc(e, t, n) { if (n & 4194240) { var r = t.lanes; r &= e.pendingLanes, n |= r, t.lanes = n, Us(e, n) } } var Sl = { readContext: Me, useCallback: ie, useContext: ie, useEffect: ie, useImperativeHandle: ie, useInsertionEffect: ie, useLayoutEffect: ie, useMemo: ie, useReducer: ie, useRef: ie, useState: ie, useDebugValue: ie, useDeferredValue: ie, useTransition: ie, useMutableSource: ie, useSyncExternalStore: ie, useId: ie, unstable_isNewReconciler: !1 }, Dm = { readContext: Me, useCallback: function (e, t) { return We().memoizedState = [e, t === void 0 ? null : t], e }, useContext: Me, useEffect: po, useImperativeHandle: function (e, t, n) { return n = n != null ? n.concat([e]) : null, el(4194308, 4, Sc.bind(null, t, e), n) }, useLayoutEffect: function (e, t) { return el(4194308, 4, e, t) }, useInsertionEffect: function (e, t) { return el(4, 2, e, t) }, useMemo: function (e, t) { var n = We(); return t = t === void 0 ? null : t, e = e(), n.memoizedState = [e, t], e }, useReducer: function (e, t, n) { var r = We(); return t = n !== void 0 ? n(t) : t, r.memoizedState = r.baseState = t, e = { pending: null, interleaved: null, lanes: 0, dispatch: null, lastRenderedReducer: e, lastRenderedState: t }, r.queue = e, e = e.dispatch = Mm.bind(null, H, e), [r.memoizedState, e] }, useRef: function (e) { var t = We(); return e = { current: e }, t.memoizedState = e }, useState: mo, useDebugValue: ua, useDeferredValue: function (e) { return We().memoizedState = e }, useTransition: function () { var e = mo(!1), t = e[0]; return e = bm.bind(null, e[1]), We().memoizedState = e, [t, e] }, useMutableSource: function () { }, useSyncExternalStore: function (e, t, n) { var r = H, l = We(); if (A) { if (n === void 0) throw Error(S(407)); n = n() } else { if (n = t(), te === null) throw Error(S(349)); Bt & 30 || hc(r, t, n) } l.memoizedState = n; var i = { value: n, getSnapshot: t }; return l.queue = i, po(yc.bind(null, r, i, e), [e]), r.flags |= 2048, vr(9, gc.bind(null, r, i, n, t), void 0, null), n }, useId: function () { var e = We(), t = te.identifierPrefix; if (A) { var n = Xe, r = Ke; n = (r & ~(1 << 32 - Ie(r) - 1)).toString(32) + n, t = ":" + t + "R" + n, n = gr++, 0 < n && (t += "H" + n.toString(32)), t += ":" } else n = _m++, t = ":" + t + "r" + n.toString(32) + ":"; return e.memoizedState = t }, unstable_isNewReconciler: !1 }, Rm = { readContext: Me, useCallback: Pc, useContext: Me, useEffect: oa, useImperativeHandle: kc, useInsertionEffect: Nc, useLayoutEffect: jc, useMemo: Cc, useReducer: ki, useRef: wc, useState: function () { return ki(yr) }, useDebugValue: ua, useDeferredValue: function (e) { var t = Te(); return Ec(t, X.memoizedState, e) }, useTransition: function () { var e = ki(yr)[0], t = Te().memoizedState; return [e, t] }, useMutableSource: mc, useSyncExternalStore: pc, useId: _c, unstable_isNewReconciler: !1 }, Om = { readContext: Me, useCallback: Pc, useContext: Me, useEffect: oa, useImperativeHandle: kc, useInsertionEffect: Nc, useLayoutEffect: jc, useMemo: Cc, useReducer: Pi, useRef: wc, useState: function () { return Pi(yr) }, useDebugValue: ua, useDeferredValue: function (e) { var t = Te(); return X === null ? t.memoizedState = e : Ec(t, X.memoizedState, e) }, useTransition: function () { var e = Pi(yr)[0], t = Te().memoizedState; return [e, t] }, useMutableSource: mc, useSyncExternalStore: pc, useId: _c, unstable_isNewReconciler: !1 }; function Re(e, t) { if (e && e.defaultProps) { t = Q({}, t), e = e.defaultProps; for (var n in e) t[n] === void 0 && (t[n] = e[n]); return t } return t } function os(e, t, n, r) { t = e.memoizedState, n = n(r, t), n = n == null ? t : Q({}, t, n), e.memoizedState = n, e.lanes === 0 && (e.updateQueue.baseState = n) } var Wl = { isMounted: function (e) { return (e = e._reactInternals) ? qt(e) === e : !1 }, enqueueSetState: function (e, t, n) { e = e._reactInternals; var r = ce(), l = wt(e), i = Je(r, l); i.payload = t, n != null && (i.callback = n), t = vt(e, i, l), t !== null && (ze(t, e, l, r), Jr(t, e, l)) }, enqueueReplaceState: function (e, t, n) { e = e._reactInternals; var r = ce(), l = wt(e), i = Je(r, l); i.tag = 1, i.payload = t, n != null && (i.callback = n), t = vt(e, i, l), t !== null && (ze(t, e, l, r), Jr(t, e, l)) }, enqueueForceUpdate: function (e, t) { e = e._reactInternals; var n = ce(), r = wt(e), l = Je(n, r); l.tag = 2, t != null && (l.callback = t), t = vt(e, l, r), t !== null && (ze(t, e, r, n), Jr(t, e, r)) } }; function ho(e, t, n, r, l, i, a) { return e = e.stateNode, typeof e.shouldComponentUpdate == "function" ? e.shouldComponentUpdate(r, i, a) : t.prototype && t.prototype.isPureReactComponent ? !cr(n, r) || !cr(l, i) : !0 } function Dc(e, t, n) { var r = !1, l = kt, i = t.contextType; return typeof i == "object" && i !== null ? i = Me(i) : (l = ye(t) ? At : oe.current, r = t.contextTypes, i = (r = r != null) ? yn(e, l) : kt), t = new t(n, i), e.memoizedState = t.state !== null && t.state !== void 0 ? t.state : null, t.updater = Wl, e.stateNode = t, t._reactInternals = e, r && (e = e.stateNode, e.__reactInternalMemoizedUnmaskedChildContext = l, e.__reactInternalMemoizedMaskedChildContext = i), t } function go(e, t, n, r) { e = t.state, typeof t.componentWillReceiveProps == "function" && t.componentWillReceiveProps(n, r), typeof t.UNSAFE_componentWillReceiveProps == "function" && t.UNSAFE_componentWillReceiveProps(n, r), t.state !== e && Wl.enqueueReplaceState(t, t.state, null) } function us(e, t, n, r) { var l = e.stateNode; l.props = n, l.state = e.memoizedState, l.refs = {}, ta(e); var i = t.contextType; typeof i == "object" && i !== null ? l.context = Me(i) : (i = ye(t) ? At : oe.current, l.context = yn(e, i)), l.state = e.memoizedState, i = t.getDerivedStateFromProps, typeof i == "function" && (os(e, t, i, n), l.state = e.memoizedState), typeof t.getDerivedStateFromProps == "function" || typeof l.getSnapshotBeforeUpdate == "function" || typeof l.UNSAFE_componentWillMount != "function" && typeof l.componentWillMount != "function" || (t = l.state, typeof l.componentWillMount == "function" && l.componentWillMount(), typeof l.UNSAFE_componentWillMount == "function" && l.UNSAFE_componentWillMount(), t !== l.state && Wl.enqueueReplaceState(l, l.state, null), wl(e, n, l, r), l.state = e.memoizedState), typeof l.componentDidMount == "function" && (e.flags |= 4194308) } function Nn(e, t) {
    try { var n = "", r = t; do n += of(r), r = r.return; while (r); var l = n } catch (i) {
        l = `
Error generating stack: `+ i.message + `
`+ i.stack
    } return { value: e, source: t, stack: l, digest: null }
} function Ci(e, t, n) { return { value: e, source: null, stack: n ?? null, digest: t ?? null } } function cs(e, t) { try { console.error(t.value) } catch (n) { setTimeout(function () { throw n }) } } var Lm = typeof WeakMap == "function" ? WeakMap : Map; function Rc(e, t, n) { n = Je(-1, n), n.tag = 3, n.payload = { element: null }; var r = t.value; return n.callback = function () { Pl || (Pl = !0, ws = r), cs(e, t) }, n } function Oc(e, t, n) { n = Je(-1, n), n.tag = 3; var r = e.type.getDerivedStateFromError; if (typeof r == "function") { var l = t.value; n.payload = function () { return r(l) }, n.callback = function () { cs(e, t) } } var i = e.stateNode; return i !== null && typeof i.componentDidCatch == "function" && (n.callback = function () { cs(e, t), typeof r != "function" && (xt === null ? xt = new Set([this]) : xt.add(this)); var a = t.stack; this.componentDidCatch(t.value, { componentStack: a !== null ? a : "" }) }), n } function yo(e, t, n) { var r = e.pingCache; if (r === null) { r = e.pingCache = new Lm; var l = new Set; r.set(t, l) } else l = r.get(t), l === void 0 && (l = new Set, r.set(t, l)); l.has(n) || (l.add(n), e = Gm.bind(null, e, t, n), t.then(e, e)) } function vo(e) { do { var t; if ((t = e.tag === 13) && (t = e.memoizedState, t = t !== null ? t.dehydrated !== null : !0), t) return e; e = e.return } while (e !== null); return null } function xo(e, t, n, r, l) { return e.mode & 1 ? (e.flags |= 65536, e.lanes = l, e) : (e === t ? e.flags |= 65536 : (e.flags |= 128, n.flags |= 131072, n.flags &= -52805, n.tag === 1 && (n.alternate === null ? n.tag = 17 : (t = Je(-1, 1), t.tag = 2, vt(n, t, 1))), n.lanes |= 1), e) } var Im = rt.ReactCurrentOwner, he = !1; function ue(e, t, n, r) { t.child = e === null ? uc(t, null, n, r) : xn(t, e.child, n, r) } function wo(e, t, n, r, l) { n = n.render; var i = t.ref; return pn(t, l), r = sa(e, t, n, r, i, l), n = aa(), e !== null && !he ? (t.updateQueue = e.updateQueue, t.flags &= -2053, e.lanes &= ~l, nt(e, t, l)) : (A && n && qs(t), t.flags |= 1, ue(e, t, r, l), t.child) } function No(e, t, n, r, l) { if (e === null) { var i = n.type; return typeof i == "function" && !ya(i) && i.defaultProps === void 0 && n.compare === null && n.defaultProps === void 0 ? (t.tag = 15, t.type = i, Lc(e, t, i, r, l)) : (e = ll(n.type, null, r, t, t.mode, l), e.ref = t.ref, e.return = t, t.child = e) } if (i = e.child, !(e.lanes & l)) { var a = i.memoizedProps; if (n = n.compare, n = n !== null ? n : cr, n(a, r) && e.ref === t.ref) return nt(e, t, l) } return t.flags |= 1, e = Nt(i, r), e.ref = t.ref, e.return = t, t.child = e } function Lc(e, t, n, r, l) { if (e !== null) { var i = e.memoizedProps; if (cr(i, r) && e.ref === t.ref) if (he = !1, t.pendingProps = r = i, (e.lanes & l) !== 0) e.flags & 131072 && (he = !0); else return t.lanes = e.lanes, nt(e, t, l) } return ds(e, t, n, r, l) } function Ic(e, t, n) { var r = t.pendingProps, l = r.children, i = e !== null ? e.memoizedState : null; if (r.mode === "hidden") if (!(t.mode & 1)) t.memoizedState = { baseLanes: 0, cachePool: null, transitions: null }, z(un, we), we |= n; else { if (!(n & 1073741824)) return e = i !== null ? i.baseLanes | n : n, t.lanes = t.childLanes = 1073741824, t.memoizedState = { baseLanes: e, cachePool: null, transitions: null }, t.updateQueue = null, z(un, we), we |= e, null; t.memoizedState = { baseLanes: 0, cachePool: null, transitions: null }, r = i !== null ? i.baseLanes : n, z(un, we), we |= r } else i !== null ? (r = i.baseLanes | n, t.memoizedState = null) : r = n, z(un, we), we |= r; return ue(e, t, l, n), t.child } function zc(e, t) { var n = t.ref; (e === null && n !== null || e !== null && e.ref !== n) && (t.flags |= 512, t.flags |= 2097152) } function ds(e, t, n, r, l) { var i = ye(n) ? At : oe.current; return i = yn(t, i), pn(t, l), n = sa(e, t, n, r, i, l), r = aa(), e !== null && !he ? (t.updateQueue = e.updateQueue, t.flags &= -2053, e.lanes &= ~l, nt(e, t, l)) : (A && r && qs(t), t.flags |= 1, ue(e, t, n, l), t.child) } function jo(e, t, n, r, l) { if (ye(n)) { var i = !0; hl(t) } else i = !1; if (pn(t, l), t.stateNode === null) tl(e, t), Dc(t, n, r), us(t, n, r, l), r = !0; else if (e === null) { var a = t.stateNode, u = t.memoizedProps; a.props = u; var o = a.context, c = n.contextType; typeof c == "object" && c !== null ? c = Me(c) : (c = ye(n) ? At : oe.current, c = yn(t, c)); var g = n.getDerivedStateFromProps, m = typeof g == "function" || typeof a.getSnapshotBeforeUpdate == "function"; m || typeof a.UNSAFE_componentWillReceiveProps != "function" && typeof a.componentWillReceiveProps != "function" || (u !== r || o !== c) && go(t, a, r, c), ot = !1; var h = t.memoizedState; a.state = h, wl(t, r, a, l), o = t.memoizedState, u !== r || h !== o || ge.current || ot ? (typeof g == "function" && (os(t, n, g, r), o = t.memoizedState), (u = ot || ho(t, n, u, r, h, o, c)) ? (m || typeof a.UNSAFE_componentWillMount != "function" && typeof a.componentWillMount != "function" || (typeof a.componentWillMount == "function" && a.componentWillMount(), typeof a.UNSAFE_componentWillMount == "function" && a.UNSAFE_componentWillMount()), typeof a.componentDidMount == "function" && (t.flags |= 4194308)) : (typeof a.componentDidMount == "function" && (t.flags |= 4194308), t.memoizedProps = r, t.memoizedState = o), a.props = r, a.state = o, a.context = c, r = u) : (typeof a.componentDidMount == "function" && (t.flags |= 4194308), r = !1) } else { a = t.stateNode, dc(e, t), u = t.memoizedProps, c = t.type === t.elementType ? u : Re(t.type, u), a.props = c, m = t.pendingProps, h = a.context, o = n.contextType, typeof o == "object" && o !== null ? o = Me(o) : (o = ye(n) ? At : oe.current, o = yn(t, o)); var w = n.getDerivedStateFromProps; (g = typeof w == "function" || typeof a.getSnapshotBeforeUpdate == "function") || typeof a.UNSAFE_componentWillReceiveProps != "function" && typeof a.componentWillReceiveProps != "function" || (u !== m || h !== o) && go(t, a, r, o), ot = !1, h = t.memoizedState, a.state = h, wl(t, r, a, l); var y = t.memoizedState; u !== m || h !== y || ge.current || ot ? (typeof w == "function" && (os(t, n, w, r), y = t.memoizedState), (c = ot || ho(t, n, c, r, h, y, o) || !1) ? (g || typeof a.UNSAFE_componentWillUpdate != "function" && typeof a.componentWillUpdate != "function" || (typeof a.componentWillUpdate == "function" && a.componentWillUpdate(r, y, o), typeof a.UNSAFE_componentWillUpdate == "function" && a.UNSAFE_componentWillUpdate(r, y, o)), typeof a.componentDidUpdate == "function" && (t.flags |= 4), typeof a.getSnapshotBeforeUpdate == "function" && (t.flags |= 1024)) : (typeof a.componentDidUpdate != "function" || u === e.memoizedProps && h === e.memoizedState || (t.flags |= 4), typeof a.getSnapshotBeforeUpdate != "function" || u === e.memoizedProps && h === e.memoizedState || (t.flags |= 1024), t.memoizedProps = r, t.memoizedState = y), a.props = r, a.state = y, a.context = o, r = c) : (typeof a.componentDidUpdate != "function" || u === e.memoizedProps && h === e.memoizedState || (t.flags |= 4), typeof a.getSnapshotBeforeUpdate != "function" || u === e.memoizedProps && h === e.memoizedState || (t.flags |= 1024), r = !1) } return fs(e, t, n, r, i, l) } function fs(e, t, n, r, l, i) { zc(e, t); var a = (t.flags & 128) !== 0; if (!r && !a) return l && so(t, n, !1), nt(e, t, i); r = t.stateNode, Im.current = t; var u = a && typeof n.getDerivedStateFromError != "function" ? null : r.render(); return t.flags |= 1, e !== null && a ? (t.child = xn(t, e.child, null, i), t.child = xn(t, null, u, i)) : ue(e, t, u, i), t.memoizedState = r.state, l && so(t, n, !0), t.child } function Fc(e) { var t = e.stateNode; t.pendingContext ? io(e, t.pendingContext, t.pendingContext !== t.context) : t.context && io(e, t.context, !1), na(e, t.containerInfo) } function So(e, t, n, r, l) { return vn(), Ks(l), t.flags |= 256, ue(e, t, n, r), t.child } var ms = { dehydrated: null, treeContext: null, retryLane: 0 }; function ps(e) { return { baseLanes: e, cachePool: null, transitions: null } } function $c(e, t, n) { var r = t.pendingProps, l = B.current, i = !1, a = (t.flags & 128) !== 0, u; if ((u = a) || (u = e !== null && e.memoizedState === null ? !1 : (l & 2) !== 0), u ? (i = !0, t.flags &= -129) : (e === null || e.memoizedState !== null) && (l |= 1), z(B, l & 1), e === null) return ss(t), e = t.memoizedState, e !== null && (e = e.dehydrated, e !== null) ? (t.mode & 1 ? e.data === "$!" ? t.lanes = 8 : t.lanes = 1073741824 : t.lanes = 1, null) : (a = r.children, e = r.fallback, i ? (r = t.mode, i = t.child, a = { mode: "hidden", children: a }, !(r & 1) && i !== null ? (i.childLanes = 0, i.pendingProps = a) : i = Vl(a, r, 0, null), e = $t(e, r, n, null), i.return = t, e.return = t, i.sibling = e, t.child = i, t.child.memoizedState = ps(n), t.memoizedState = ms, e) : ca(t, a)); if (l = e.memoizedState, l !== null && (u = l.dehydrated, u !== null)) return zm(e, t, a, r, u, l, n); if (i) { i = r.fallback, a = t.mode, l = e.child, u = l.sibling; var o = { mode: "hidden", children: r.children }; return !(a & 1) && t.child !== l ? (r = t.child, r.childLanes = 0, r.pendingProps = o, t.deletions = null) : (r = Nt(l, o), r.subtreeFlags = l.subtreeFlags & 14680064), u !== null ? i = Nt(u, i) : (i = $t(i, a, n, null), i.flags |= 2), i.return = t, r.return = t, r.sibling = i, t.child = r, r = i, i = t.child, a = e.child.memoizedState, a = a === null ? ps(n) : { baseLanes: a.baseLanes | n, cachePool: null, transitions: a.transitions }, i.memoizedState = a, i.childLanes = e.childLanes & ~n, t.memoizedState = ms, r } return i = e.child, e = i.sibling, r = Nt(i, { mode: "visible", children: r.children }), !(t.mode & 1) && (r.lanes = n), r.return = t, r.sibling = null, e !== null && (n = t.deletions, n === null ? (t.deletions = [e], t.flags |= 16) : n.push(e)), t.child = r, t.memoizedState = null, r } function ca(e, t) { return t = Vl({ mode: "visible", children: t }, e.mode, 0, null), t.return = e, e.child = t } function Br(e, t, n, r) { return r !== null && Ks(r), xn(t, e.child, null, n), e = ca(t, t.pendingProps.children), e.flags |= 2, t.memoizedState = null, e } function zm(e, t, n, r, l, i, a) { if (n) return t.flags & 256 ? (t.flags &= -257, r = Ci(Error(S(422))), Br(e, t, a, r)) : t.memoizedState !== null ? (t.child = e.child, t.flags |= 128, null) : (i = r.fallback, l = t.mode, r = Vl({ mode: "visible", children: r.children }, l, 0, null), i = $t(i, l, a, null), i.flags |= 2, r.return = t, i.return = t, r.sibling = i, t.child = r, t.mode & 1 && xn(t, e.child, null, a), t.child.memoizedState = ps(a), t.memoizedState = ms, i); if (!(t.mode & 1)) return Br(e, t, a, null); if (l.data === "$!") { if (r = l.nextSibling && l.nextSibling.dataset, r) var u = r.dgst; return r = u, i = Error(S(419)), r = Ci(i, r, void 0), Br(e, t, a, r) } if (u = (a & e.childLanes) !== 0, he || u) { if (r = te, r !== null) { switch (a & -a) { case 4: l = 2; break; case 16: l = 8; break; case 64: case 128: case 256: case 512: case 1024: case 2048: case 4096: case 8192: case 16384: case 32768: case 65536: case 131072: case 262144: case 524288: case 1048576: case 2097152: case 4194304: case 8388608: case 16777216: case 33554432: case 67108864: l = 32; break; case 536870912: l = 268435456; break; default: l = 0 }l = l & (r.suspendedLanes | a) ? 0 : l, l !== 0 && l !== i.retryLane && (i.retryLane = l, tt(e, l), ze(r, e, l, -1)) } return ga(), r = Ci(Error(S(421))), Br(e, t, a, r) } return l.data === "$?" ? (t.flags |= 128, t.child = e.child, t = Km.bind(null, e), l._reactRetry = t, null) : (e = i.treeContext, Ne = yt(l.nextSibling), je = t, A = !0, Le = null, e !== null && (Ce[Ee++] = Ke, Ce[Ee++] = Xe, Ce[Ee++] = Wt, Ke = e.id, Xe = e.overflow, Wt = t), t = ca(t, r.children), t.flags |= 4096, t) } function ko(e, t, n) { e.lanes |= t; var r = e.alternate; r !== null && (r.lanes |= t), as(e.return, t, n) } function Ei(e, t, n, r, l) { var i = e.memoizedState; i === null ? e.memoizedState = { isBackwards: t, rendering: null, renderingStartTime: 0, last: r, tail: n, tailMode: l } : (i.isBackwards = t, i.rendering = null, i.renderingStartTime = 0, i.last = r, i.tail = n, i.tailMode = l) } function Uc(e, t, n) { var r = t.pendingProps, l = r.revealOrder, i = r.tail; if (ue(e, t, r.children, n), r = B.current, r & 2) r = r & 1 | 2, t.flags |= 128; else { if (e !== null && e.flags & 128) e: for (e = t.child; e !== null;) { if (e.tag === 13) e.memoizedState !== null && ko(e, n, t); else if (e.tag === 19) ko(e, n, t); else if (e.child !== null) { e.child.return = e, e = e.child; continue } if (e === t) break e; for (; e.sibling === null;) { if (e.return === null || e.return === t) break e; e = e.return } e.sibling.return = e.return, e = e.sibling } r &= 1 } if (z(B, r), !(t.mode & 1)) t.memoizedState = null; else switch (l) { case "forwards": for (n = t.child, l = null; n !== null;)e = n.alternate, e !== null && Nl(e) === null && (l = n), n = n.sibling; n = l, n === null ? (l = t.child, t.child = null) : (l = n.sibling, n.sibling = null), Ei(t, !1, l, n, i); break; case "backwards": for (n = null, l = t.child, t.child = null; l !== null;) { if (e = l.alternate, e !== null && Nl(e) === null) { t.child = l; break } e = l.sibling, l.sibling = n, n = l, l = e } Ei(t, !0, n, null, i); break; case "together": Ei(t, !1, null, null, void 0); break; default: t.memoizedState = null }return t.child } function tl(e, t) { !(t.mode & 1) && e !== null && (e.alternate = null, t.alternate = null, t.flags |= 2) } function nt(e, t, n) { if (e !== null && (t.dependencies = e.dependencies), Ht |= t.lanes, !(n & t.childLanes)) return null; if (e !== null && t.child !== e.child) throw Error(S(153)); if (t.child !== null) { for (e = t.child, n = Nt(e, e.pendingProps), t.child = n, n.return = t; e.sibling !== null;)e = e.sibling, n = n.sibling = Nt(e, e.pendingProps), n.return = t; n.sibling = null } return t.child } function Fm(e, t, n) { switch (t.tag) { case 3: Fc(t), vn(); break; case 5: fc(t); break; case 1: ye(t.type) && hl(t); break; case 4: na(t, t.stateNode.containerInfo); break; case 10: var r = t.type._context, l = t.memoizedProps.value; z(vl, r._currentValue), r._currentValue = l; break; case 13: if (r = t.memoizedState, r !== null) return r.dehydrated !== null ? (z(B, B.current & 1), t.flags |= 128, null) : n & t.child.childLanes ? $c(e, t, n) : (z(B, B.current & 1), e = nt(e, t, n), e !== null ? e.sibling : null); z(B, B.current & 1); break; case 19: if (r = (n & t.childLanes) !== 0, e.flags & 128) { if (r) return Uc(e, t, n); t.flags |= 128 } if (l = t.memoizedState, l !== null && (l.rendering = null, l.tail = null, l.lastEffect = null), z(B, B.current), r) break; return null; case 22: case 23: return t.lanes = 0, Ic(e, t, n) }return nt(e, t, n) } var Ac, hs, Wc, Bc; Ac = function (e, t) { for (var n = t.child; n !== null;) { if (n.tag === 5 || n.tag === 6) e.appendChild(n.stateNode); else if (n.tag !== 4 && n.child !== null) { n.child.return = n, n = n.child; continue } if (n === t) break; for (; n.sibling === null;) { if (n.return === null || n.return === t) return; n = n.return } n.sibling.return = n.return, n = n.sibling } }; hs = function () { }; Wc = function (e, t, n, r) { var l = e.memoizedProps; if (l !== r) { e = t.stateNode, zt(Qe.current); var i = null; switch (n) { case "input": l = zi(e, l), r = zi(e, r), i = []; break; case "select": l = Q({}, l, { value: void 0 }), r = Q({}, r, { value: void 0 }), i = []; break; case "textarea": l = Ui(e, l), r = Ui(e, r), i = []; break; default: typeof l.onClick != "function" && typeof r.onClick == "function" && (e.onclick = ml) }Wi(n, r); var a; n = null; for (c in l) if (!r.hasOwnProperty(c) && l.hasOwnProperty(c) && l[c] != null) if (c === "style") { var u = l[c]; for (a in u) u.hasOwnProperty(a) && (n || (n = {}), n[a] = "") } else c !== "dangerouslySetInnerHTML" && c !== "children" && c !== "suppressContentEditableWarning" && c !== "suppressHydrationWarning" && c !== "autoFocus" && (rr.hasOwnProperty(c) ? i || (i = []) : (i = i || []).push(c, null)); for (c in r) { var o = r[c]; if (u = l != null ? l[c] : void 0, r.hasOwnProperty(c) && o !== u && (o != null || u != null)) if (c === "style") if (u) { for (a in u) !u.hasOwnProperty(a) || o && o.hasOwnProperty(a) || (n || (n = {}), n[a] = ""); for (a in o) o.hasOwnProperty(a) && u[a] !== o[a] && (n || (n = {}), n[a] = o[a]) } else n || (i || (i = []), i.push(c, n)), n = o; else c === "dangerouslySetInnerHTML" ? (o = o ? o.__html : void 0, u = u ? u.__html : void 0, o != null && u !== o && (i = i || []).push(c, o)) : c === "children" ? typeof o != "string" && typeof o != "number" || (i = i || []).push(c, "" + o) : c !== "suppressContentEditableWarning" && c !== "suppressHydrationWarning" && (rr.hasOwnProperty(c) ? (o != null && c === "onScroll" && F("scroll", e), i || u === o || (i = [])) : (i = i || []).push(c, o)) } n && (i = i || []).push("style", n); var c = i; (t.updateQueue = c) && (t.flags |= 4) } }; Bc = function (e, t, n, r) { n !== r && (t.flags |= 4) }; function $n(e, t) { if (!A) switch (e.tailMode) { case "hidden": t = e.tail; for (var n = null; t !== null;)t.alternate !== null && (n = t), t = t.sibling; n === null ? e.tail = null : n.sibling = null; break; case "collapsed": n = e.tail; for (var r = null; n !== null;)n.alternate !== null && (r = n), n = n.sibling; r === null ? t || e.tail === null ? e.tail = null : e.tail.sibling = null : r.sibling = null } } function se(e) { var t = e.alternate !== null && e.alternate.child === e.child, n = 0, r = 0; if (t) for (var l = e.child; l !== null;)n |= l.lanes | l.childLanes, r |= l.subtreeFlags & 14680064, r |= l.flags & 14680064, l.return = e, l = l.sibling; else for (l = e.child; l !== null;)n |= l.lanes | l.childLanes, r |= l.subtreeFlags, r |= l.flags, l.return = e, l = l.sibling; return e.subtreeFlags |= r, e.childLanes = n, t } function $m(e, t, n) { var r = t.pendingProps; switch (Gs(t), t.tag) { case 2: case 16: case 15: case 0: case 11: case 7: case 8: case 12: case 9: case 14: return se(t), null; case 1: return ye(t.type) && pl(), se(t), null; case 3: return r = t.stateNode, wn(), $(ge), $(oe), la(), r.pendingContext && (r.context = r.pendingContext, r.pendingContext = null), (e === null || e.child === null) && (Ar(t) ? t.flags |= 4 : e === null || e.memoizedState.isDehydrated && !(t.flags & 256) || (t.flags |= 1024, Le !== null && (Ss(Le), Le = null))), hs(e, t), se(t), null; case 5: ra(t); var l = zt(hr.current); if (n = t.type, e !== null && t.stateNode != null) Wc(e, t, n, r, l), e.ref !== t.ref && (t.flags |= 512, t.flags |= 2097152); else { if (!r) { if (t.stateNode === null) throw Error(S(166)); return se(t), null } if (e = zt(Qe.current), Ar(t)) { r = t.stateNode, n = t.type; var i = t.memoizedProps; switch (r[Be] = t, r[mr] = i, e = (t.mode & 1) !== 0, n) { case "dialog": F("cancel", r), F("close", r); break; case "iframe": case "object": case "embed": F("load", r); break; case "video": case "audio": for (l = 0; l < Qn.length; l++)F(Qn[l], r); break; case "source": F("error", r); break; case "img": case "image": case "link": F("error", r), F("load", r); break; case "details": F("toggle", r); break; case "input": Da(r, i), F("invalid", r); break; case "select": r._wrapperState = { wasMultiple: !!i.multiple }, F("invalid", r); break; case "textarea": Oa(r, i), F("invalid", r) }Wi(n, i), l = null; for (var a in i) if (i.hasOwnProperty(a)) { var u = i[a]; a === "children" ? typeof u == "string" ? r.textContent !== u && (i.suppressHydrationWarning !== !0 && Ur(r.textContent, u, e), l = ["children", u]) : typeof u == "number" && r.textContent !== "" + u && (i.suppressHydrationWarning !== !0 && Ur(r.textContent, u, e), l = ["children", "" + u]) : rr.hasOwnProperty(a) && u != null && a === "onScroll" && F("scroll", r) } switch (n) { case "input": Dr(r), Ra(r, i, !0); break; case "textarea": Dr(r), La(r); break; case "select": case "option": break; default: typeof i.onClick == "function" && (r.onclick = ml) }r = l, t.updateQueue = r, r !== null && (t.flags |= 4) } else { a = l.nodeType === 9 ? l : l.ownerDocument, e === "http://www.w3.org/1999/xhtml" && (e = yu(n)), e === "http://www.w3.org/1999/xhtml" ? n === "script" ? (e = a.createElement("div"), e.innerHTML = "<script><\/script>", e = e.removeChild(e.firstChild)) : typeof r.is == "string" ? e = a.createElement(n, { is: r.is }) : (e = a.createElement(n), n === "select" && (a = e, r.multiple ? a.multiple = !0 : r.size && (a.size = r.size))) : e = a.createElementNS(e, n), e[Be] = t, e[mr] = r, Ac(e, t, !1, !1), t.stateNode = e; e: { switch (a = Bi(n, r), n) { case "dialog": F("cancel", e), F("close", e), l = r; break; case "iframe": case "object": case "embed": F("load", e), l = r; break; case "video": case "audio": for (l = 0; l < Qn.length; l++)F(Qn[l], e); l = r; break; case "source": F("error", e), l = r; break; case "img": case "image": case "link": F("error", e), F("load", e), l = r; break; case "details": F("toggle", e), l = r; break; case "input": Da(e, r), l = zi(e, r), F("invalid", e); break; case "option": l = r; break; case "select": e._wrapperState = { wasMultiple: !!r.multiple }, l = Q({}, r, { value: void 0 }), F("invalid", e); break; case "textarea": Oa(e, r), l = Ui(e, r), F("invalid", e); break; default: l = r }Wi(n, l), u = l; for (i in u) if (u.hasOwnProperty(i)) { var o = u[i]; i === "style" ? wu(e, o) : i === "dangerouslySetInnerHTML" ? (o = o ? o.__html : void 0, o != null && vu(e, o)) : i === "children" ? typeof o == "string" ? (n !== "textarea" || o !== "") && lr(e, o) : typeof o == "number" && lr(e, "" + o) : i !== "suppressContentEditableWarning" && i !== "suppressHydrationWarning" && i !== "autoFocus" && (rr.hasOwnProperty(i) ? o != null && i === "onScroll" && F("scroll", e) : o != null && Os(e, i, o, a)) } switch (n) { case "input": Dr(e), Ra(e, r, !1); break; case "textarea": Dr(e), La(e); break; case "option": r.value != null && e.setAttribute("value", "" + St(r.value)); break; case "select": e.multiple = !!r.multiple, i = r.value, i != null ? cn(e, !!r.multiple, i, !1) : r.defaultValue != null && cn(e, !!r.multiple, r.defaultValue, !0); break; default: typeof l.onClick == "function" && (e.onclick = ml) }switch (n) { case "button": case "input": case "select": case "textarea": r = !!r.autoFocus; break e; case "img": r = !0; break e; default: r = !1 } } r && (t.flags |= 4) } t.ref !== null && (t.flags |= 512, t.flags |= 2097152) } return se(t), null; case 6: if (e && t.stateNode != null) Bc(e, t, e.memoizedProps, r); else { if (typeof r != "string" && t.stateNode === null) throw Error(S(166)); if (n = zt(hr.current), zt(Qe.current), Ar(t)) { if (r = t.stateNode, n = t.memoizedProps, r[Be] = t, (i = r.nodeValue !== n) && (e = je, e !== null)) switch (e.tag) { case 3: Ur(r.nodeValue, n, (e.mode & 1) !== 0); break; case 5: e.memoizedProps.suppressHydrationWarning !== !0 && Ur(r.nodeValue, n, (e.mode & 1) !== 0) }i && (t.flags |= 4) } else r = (n.nodeType === 9 ? n : n.ownerDocument).createTextNode(r), r[Be] = t, t.stateNode = r } return se(t), null; case 13: if ($(B), r = t.memoizedState, e === null || e.memoizedState !== null && e.memoizedState.dehydrated !== null) { if (A && Ne !== null && t.mode & 1 && !(t.flags & 128)) ac(), vn(), t.flags |= 98560, i = !1; else if (i = Ar(t), r !== null && r.dehydrated !== null) { if (e === null) { if (!i) throw Error(S(318)); if (i = t.memoizedState, i = i !== null ? i.dehydrated : null, !i) throw Error(S(317)); i[Be] = t } else vn(), !(t.flags & 128) && (t.memoizedState = null), t.flags |= 4; se(t), i = !1 } else Le !== null && (Ss(Le), Le = null), i = !0; if (!i) return t.flags & 65536 ? t : null } return t.flags & 128 ? (t.lanes = n, t) : (r = r !== null, r !== (e !== null && e.memoizedState !== null) && r && (t.child.flags |= 8192, t.mode & 1 && (e === null || B.current & 1 ? J === 0 && (J = 3) : ga())), t.updateQueue !== null && (t.flags |= 4), se(t), null); case 4: return wn(), hs(e, t), e === null && dr(t.stateNode.containerInfo), se(t), null; case 10: return Zs(t.type._context), se(t), null; case 17: return ye(t.type) && pl(), se(t), null; case 19: if ($(B), i = t.memoizedState, i === null) return se(t), null; if (r = (t.flags & 128) !== 0, a = i.rendering, a === null) if (r) $n(i, !1); else { if (J !== 0 || e !== null && e.flags & 128) for (e = t.child; e !== null;) { if (a = Nl(e), a !== null) { for (t.flags |= 128, $n(i, !1), r = a.updateQueue, r !== null && (t.updateQueue = r, t.flags |= 4), t.subtreeFlags = 0, r = n, n = t.child; n !== null;)i = n, e = r, i.flags &= 14680066, a = i.alternate, a === null ? (i.childLanes = 0, i.lanes = e, i.child = null, i.subtreeFlags = 0, i.memoizedProps = null, i.memoizedState = null, i.updateQueue = null, i.dependencies = null, i.stateNode = null) : (i.childLanes = a.childLanes, i.lanes = a.lanes, i.child = a.child, i.subtreeFlags = 0, i.deletions = null, i.memoizedProps = a.memoizedProps, i.memoizedState = a.memoizedState, i.updateQueue = a.updateQueue, i.type = a.type, e = a.dependencies, i.dependencies = e === null ? null : { lanes: e.lanes, firstContext: e.firstContext }), n = n.sibling; return z(B, B.current & 1 | 2), t.child } e = e.sibling } i.tail !== null && G() > jn && (t.flags |= 128, r = !0, $n(i, !1), t.lanes = 4194304) } else { if (!r) if (e = Nl(a), e !== null) { if (t.flags |= 128, r = !0, n = e.updateQueue, n !== null && (t.updateQueue = n, t.flags |= 4), $n(i, !0), i.tail === null && i.tailMode === "hidden" && !a.alternate && !A) return se(t), null } else 2 * G() - i.renderingStartTime > jn && n !== 1073741824 && (t.flags |= 128, r = !0, $n(i, !1), t.lanes = 4194304); i.isBackwards ? (a.sibling = t.child, t.child = a) : (n = i.last, n !== null ? n.sibling = a : t.child = a, i.last = a) } return i.tail !== null ? (t = i.tail, i.rendering = t, i.tail = t.sibling, i.renderingStartTime = G(), t.sibling = null, n = B.current, z(B, r ? n & 1 | 2 : n & 1), t) : (se(t), null); case 22: case 23: return ha(), r = t.memoizedState !== null, e !== null && e.memoizedState !== null !== r && (t.flags |= 8192), r && t.mode & 1 ? we & 1073741824 && (se(t), t.subtreeFlags & 6 && (t.flags |= 8192)) : se(t), null; case 24: return null; case 25: return null }throw Error(S(156, t.tag)) } function Um(e, t) { switch (Gs(t), t.tag) { case 1: return ye(t.type) && pl(), e = t.flags, e & 65536 ? (t.flags = e & -65537 | 128, t) : null; case 3: return wn(), $(ge), $(oe), la(), e = t.flags, e & 65536 && !(e & 128) ? (t.flags = e & -65537 | 128, t) : null; case 5: return ra(t), null; case 13: if ($(B), e = t.memoizedState, e !== null && e.dehydrated !== null) { if (t.alternate === null) throw Error(S(340)); vn() } return e = t.flags, e & 65536 ? (t.flags = e & -65537 | 128, t) : null; case 19: return $(B), null; case 4: return wn(), null; case 10: return Zs(t.type._context), null; case 22: case 23: return ha(), null; case 24: return null; default: return null } } var Hr = !1, ae = !1, Am = typeof WeakSet == "function" ? WeakSet : Set, E = null; function on(e, t) { var n = e.ref; if (n !== null) if (typeof n == "function") try { n(null) } catch (r) { Y(e, t, r) } else n.current = null } function gs(e, t, n) { try { n() } catch (r) { Y(e, t, r) } } var Po = !1; function Wm(e, t) { if (Zi = cl, e = qu(), Ys(e)) { if ("selectionStart" in e) var n = { start: e.selectionStart, end: e.selectionEnd }; else e: { n = (n = e.ownerDocument) && n.defaultView || window; var r = n.getSelection && n.getSelection(); if (r && r.rangeCount !== 0) { n = r.anchorNode; var l = r.anchorOffset, i = r.focusNode; r = r.focusOffset; try { n.nodeType, i.nodeType } catch { n = null; break e } var a = 0, u = -1, o = -1, c = 0, g = 0, m = e, h = null; t: for (; ;) { for (var w; m !== n || l !== 0 && m.nodeType !== 3 || (u = a + l), m !== i || r !== 0 && m.nodeType !== 3 || (o = a + r), m.nodeType === 3 && (a += m.nodeValue.length), (w = m.firstChild) !== null;)h = m, m = w; for (; ;) { if (m === e) break t; if (h === n && ++c === l && (u = a), h === i && ++g === r && (o = a), (w = m.nextSibling) !== null) break; m = h, h = m.parentNode } m = w } n = u === -1 || o === -1 ? null : { start: u, end: o } } else n = null } n = n || { start: 0, end: 0 } } else n = null; for (es = { focusedElem: e, selectionRange: n }, cl = !1, E = t; E !== null;)if (t = E, e = t.child, (t.subtreeFlags & 1028) !== 0 && e !== null) e.return = t, E = e; else for (; E !== null;) { t = E; try { var y = t.alternate; if (t.flags & 1024) switch (t.tag) { case 0: case 11: case 15: break; case 1: if (y !== null) { var x = y.memoizedProps, k = y.memoizedState, f = t.stateNode, d = f.getSnapshotBeforeUpdate(t.elementType === t.type ? x : Re(t.type, x), k); f.__reactInternalSnapshotBeforeUpdate = d } break; case 3: var p = t.stateNode.containerInfo; p.nodeType === 1 ? p.textContent = "" : p.nodeType === 9 && p.documentElement && p.removeChild(p.documentElement); break; case 5: case 6: case 4: case 17: break; default: throw Error(S(163)) } } catch (v) { Y(t, t.return, v) } if (e = t.sibling, e !== null) { e.return = t.return, E = e; break } E = t.return } return y = Po, Po = !1, y } function Zn(e, t, n) { var r = t.updateQueue; if (r = r !== null ? r.lastEffect : null, r !== null) { var l = r = r.next; do { if ((l.tag & e) === e) { var i = l.destroy; l.destroy = void 0, i !== void 0 && gs(t, n, i) } l = l.next } while (l !== r) } } function Bl(e, t) { if (t = t.updateQueue, t = t !== null ? t.lastEffect : null, t !== null) { var n = t = t.next; do { if ((n.tag & e) === e) { var r = n.create; n.destroy = r() } n = n.next } while (n !== t) } } function ys(e) { var t = e.ref; if (t !== null) { var n = e.stateNode; switch (e.tag) { case 5: e = n; break; default: e = n }typeof t == "function" ? t(e) : t.current = e } } function Hc(e) { var t = e.alternate; t !== null && (e.alternate = null, Hc(t)), e.child = null, e.deletions = null, e.sibling = null, e.tag === 5 && (t = e.stateNode, t !== null && (delete t[Be], delete t[mr], delete t[rs], delete t[km], delete t[Pm])), e.stateNode = null, e.return = null, e.dependencies = null, e.memoizedProps = null, e.memoizedState = null, e.pendingProps = null, e.stateNode = null, e.updateQueue = null } function Vc(e) { return e.tag === 5 || e.tag === 3 || e.tag === 4 } function Co(e) { e: for (; ;) { for (; e.sibling === null;) { if (e.return === null || Vc(e.return)) return null; e = e.return } for (e.sibling.return = e.return, e = e.sibling; e.tag !== 5 && e.tag !== 6 && e.tag !== 18;) { if (e.flags & 2 || e.child === null || e.tag === 4) continue e; e.child.return = e, e = e.child } if (!(e.flags & 2)) return e.stateNode } } function vs(e, t, n) { var r = e.tag; if (r === 5 || r === 6) e = e.stateNode, t ? n.nodeType === 8 ? n.parentNode.insertBefore(e, t) : n.insertBefore(e, t) : (n.nodeType === 8 ? (t = n.parentNode, t.insertBefore(e, n)) : (t = n, t.appendChild(e)), n = n._reactRootContainer, n != null || t.onclick !== null || (t.onclick = ml)); else if (r !== 4 && (e = e.child, e !== null)) for (vs(e, t, n), e = e.sibling; e !== null;)vs(e, t, n), e = e.sibling } function xs(e, t, n) { var r = e.tag; if (r === 5 || r === 6) e = e.stateNode, t ? n.insertBefore(e, t) : n.appendChild(e); else if (r !== 4 && (e = e.child, e !== null)) for (xs(e, t, n), e = e.sibling; e !== null;)xs(e, t, n), e = e.sibling } var ne = null, Oe = !1; function it(e, t, n) { for (n = n.child; n !== null;)Qc(e, t, n), n = n.sibling } function Qc(e, t, n) { if (Ve && typeof Ve.onCommitFiberUnmount == "function") try { Ve.onCommitFiberUnmount(Ll, n) } catch { } switch (n.tag) { case 5: ae || on(n, t); case 6: var r = ne, l = Oe; ne = null, it(e, t, n), ne = r, Oe = l, ne !== null && (Oe ? (e = ne, n = n.stateNode, e.nodeType === 8 ? e.parentNode.removeChild(n) : e.removeChild(n)) : ne.removeChild(n.stateNode)); break; case 18: ne !== null && (Oe ? (e = ne, n = n.stateNode, e.nodeType === 8 ? wi(e.parentNode, n) : e.nodeType === 1 && wi(e, n), or(e)) : wi(ne, n.stateNode)); break; case 4: r = ne, l = Oe, ne = n.stateNode.containerInfo, Oe = !0, it(e, t, n), ne = r, Oe = l; break; case 0: case 11: case 14: case 15: if (!ae && (r = n.updateQueue, r !== null && (r = r.lastEffect, r !== null))) { l = r = r.next; do { var i = l, a = i.destroy; i = i.tag, a !== void 0 && (i & 2 || i & 4) && gs(n, t, a), l = l.next } while (l !== r) } it(e, t, n); break; case 1: if (!ae && (on(n, t), r = n.stateNode, typeof r.componentWillUnmount == "function")) try { r.props = n.memoizedProps, r.state = n.memoizedState, r.componentWillUnmount() } catch (u) { Y(n, t, u) } it(e, t, n); break; case 21: it(e, t, n); break; case 22: n.mode & 1 ? (ae = (r = ae) || n.memoizedState !== null, it(e, t, n), ae = r) : it(e, t, n); break; default: it(e, t, n) } } function Eo(e) { var t = e.updateQueue; if (t !== null) { e.updateQueue = null; var n = e.stateNode; n === null && (n = e.stateNode = new Am), t.forEach(function (r) { var l = Xm.bind(null, e, r); n.has(r) || (n.add(r), r.then(l, l)) }) } } function De(e, t) { var n = t.deletions; if (n !== null) for (var r = 0; r < n.length; r++) { var l = n[r]; try { var i = e, a = t, u = a; e: for (; u !== null;) { switch (u.tag) { case 5: ne = u.stateNode, Oe = !1; break e; case 3: ne = u.stateNode.containerInfo, Oe = !0; break e; case 4: ne = u.stateNode.containerInfo, Oe = !0; break e }u = u.return } if (ne === null) throw Error(S(160)); Qc(i, a, l), ne = null, Oe = !1; var o = l.alternate; o !== null && (o.return = null), l.return = null } catch (c) { Y(l, t, c) } } if (t.subtreeFlags & 12854) for (t = t.child; t !== null;)Yc(t, e), t = t.sibling } function Yc(e, t) { var n = e.alternate, r = e.flags; switch (e.tag) { case 0: case 11: case 14: case 15: if (De(t, e), Ue(e), r & 4) { try { Zn(3, e, e.return), Bl(3, e) } catch (x) { Y(e, e.return, x) } try { Zn(5, e, e.return) } catch (x) { Y(e, e.return, x) } } break; case 1: De(t, e), Ue(e), r & 512 && n !== null && on(n, n.return); break; case 5: if (De(t, e), Ue(e), r & 512 && n !== null && on(n, n.return), e.flags & 32) { var l = e.stateNode; try { lr(l, "") } catch (x) { Y(e, e.return, x) } } if (r & 4 && (l = e.stateNode, l != null)) { var i = e.memoizedProps, a = n !== null ? n.memoizedProps : i, u = e.type, o = e.updateQueue; if (e.updateQueue = null, o !== null) try { u === "input" && i.type === "radio" && i.name != null && hu(l, i), Bi(u, a); var c = Bi(u, i); for (a = 0; a < o.length; a += 2) { var g = o[a], m = o[a + 1]; g === "style" ? wu(l, m) : g === "dangerouslySetInnerHTML" ? vu(l, m) : g === "children" ? lr(l, m) : Os(l, g, m, c) } switch (u) { case "input": Fi(l, i); break; case "textarea": gu(l, i); break; case "select": var h = l._wrapperState.wasMultiple; l._wrapperState.wasMultiple = !!i.multiple; var w = i.value; w != null ? cn(l, !!i.multiple, w, !1) : h !== !!i.multiple && (i.defaultValue != null ? cn(l, !!i.multiple, i.defaultValue, !0) : cn(l, !!i.multiple, i.multiple ? [] : "", !1)) }l[mr] = i } catch (x) { Y(e, e.return, x) } } break; case 6: if (De(t, e), Ue(e), r & 4) { if (e.stateNode === null) throw Error(S(162)); l = e.stateNode, i = e.memoizedProps; try { l.nodeValue = i } catch (x) { Y(e, e.return, x) } } break; case 3: if (De(t, e), Ue(e), r & 4 && n !== null && n.memoizedState.isDehydrated) try { or(t.containerInfo) } catch (x) { Y(e, e.return, x) } break; case 4: De(t, e), Ue(e); break; case 13: De(t, e), Ue(e), l = e.child, l.flags & 8192 && (i = l.memoizedState !== null, l.stateNode.isHidden = i, !i || l.alternate !== null && l.alternate.memoizedState !== null || (ma = G())), r & 4 && Eo(e); break; case 22: if (g = n !== null && n.memoizedState !== null, e.mode & 1 ? (ae = (c = ae) || g, De(t, e), ae = c) : De(t, e), Ue(e), r & 8192) { if (c = e.memoizedState !== null, (e.stateNode.isHidden = c) && !g && e.mode & 1) for (E = e, g = e.child; g !== null;) { for (m = E = g; E !== null;) { switch (h = E, w = h.child, h.tag) { case 0: case 11: case 14: case 15: Zn(4, h, h.return); break; case 1: on(h, h.return); var y = h.stateNode; if (typeof y.componentWillUnmount == "function") { r = h, n = h.return; try { t = r, y.props = t.memoizedProps, y.state = t.memoizedState, y.componentWillUnmount() } catch (x) { Y(r, n, x) } } break; case 5: on(h, h.return); break; case 22: if (h.memoizedState !== null) { bo(m); continue } }w !== null ? (w.return = h, E = w) : bo(m) } g = g.sibling } e: for (g = null, m = e; ;) { if (m.tag === 5) { if (g === null) { g = m; try { l = m.stateNode, c ? (i = l.style, typeof i.setProperty == "function" ? i.setProperty("display", "none", "important") : i.display = "none") : (u = m.stateNode, o = m.memoizedProps.style, a = o != null && o.hasOwnProperty("display") ? o.display : null, u.style.display = xu("display", a)) } catch (x) { Y(e, e.return, x) } } } else if (m.tag === 6) { if (g === null) try { m.stateNode.nodeValue = c ? "" : m.memoizedProps } catch (x) { Y(e, e.return, x) } } else if ((m.tag !== 22 && m.tag !== 23 || m.memoizedState === null || m === e) && m.child !== null) { m.child.return = m, m = m.child; continue } if (m === e) break e; for (; m.sibling === null;) { if (m.return === null || m.return === e) break e; g === m && (g = null), m = m.return } g === m && (g = null), m.sibling.return = m.return, m = m.sibling } } break; case 19: De(t, e), Ue(e), r & 4 && Eo(e); break; case 21: break; default: De(t, e), Ue(e) } } function Ue(e) { var t = e.flags; if (t & 2) { try { e: { for (var n = e.return; n !== null;) { if (Vc(n)) { var r = n; break e } n = n.return } throw Error(S(160)) } switch (r.tag) { case 5: var l = r.stateNode; r.flags & 32 && (lr(l, ""), r.flags &= -33); var i = Co(e); xs(e, i, l); break; case 3: case 4: var a = r.stateNode.containerInfo, u = Co(e); vs(e, u, a); break; default: throw Error(S(161)) } } catch (o) { Y(e, e.return, o) } e.flags &= -3 } t & 4096 && (e.flags &= -4097) } function Bm(e, t, n) { E = e, qc(e) } function qc(e, t, n) { for (var r = (e.mode & 1) !== 0; E !== null;) { var l = E, i = l.child; if (l.tag === 22 && r) { var a = l.memoizedState !== null || Hr; if (!a) { var u = l.alternate, o = u !== null && u.memoizedState !== null || ae; u = Hr; var c = ae; if (Hr = a, (ae = o) && !c) for (E = l; E !== null;)a = E, o = a.child, a.tag === 22 && a.memoizedState !== null ? Mo(l) : o !== null ? (o.return = a, E = o) : Mo(l); for (; i !== null;)E = i, qc(i), i = i.sibling; E = l, Hr = u, ae = c } _o(e) } else l.subtreeFlags & 8772 && i !== null ? (i.return = l, E = i) : _o(e) } } function _o(e) { for (; E !== null;) { var t = E; if (t.flags & 8772) { var n = t.alternate; try { if (t.flags & 8772) switch (t.tag) { case 0: case 11: case 15: ae || Bl(5, t); break; case 1: var r = t.stateNode; if (t.flags & 4 && !ae) if (n === null) r.componentDidMount(); else { var l = t.elementType === t.type ? n.memoizedProps : Re(t.type, n.memoizedProps); r.componentDidUpdate(l, n.memoizedState, r.__reactInternalSnapshotBeforeUpdate) } var i = t.updateQueue; i !== null && fo(t, i, r); break; case 3: var a = t.updateQueue; if (a !== null) { if (n = null, t.child !== null) switch (t.child.tag) { case 5: n = t.child.stateNode; break; case 1: n = t.child.stateNode }fo(t, a, n) } break; case 5: var u = t.stateNode; if (n === null && t.flags & 4) { n = u; var o = t.memoizedProps; switch (t.type) { case "button": case "input": case "select": case "textarea": o.autoFocus && n.focus(); break; case "img": o.src && (n.src = o.src) } } break; case 6: break; case 4: break; case 12: break; case 13: if (t.memoizedState === null) { var c = t.alternate; if (c !== null) { var g = c.memoizedState; if (g !== null) { var m = g.dehydrated; m !== null && or(m) } } } break; case 19: case 17: case 21: case 22: case 23: case 25: break; default: throw Error(S(163)) }ae || t.flags & 512 && ys(t) } catch (h) { Y(t, t.return, h) } } if (t === e) { E = null; break } if (n = t.sibling, n !== null) { n.return = t.return, E = n; break } E = t.return } } function bo(e) { for (; E !== null;) { var t = E; if (t === e) { E = null; break } var n = t.sibling; if (n !== null) { n.return = t.return, E = n; break } E = t.return } } function Mo(e) { for (; E !== null;) { var t = E; try { switch (t.tag) { case 0: case 11: case 15: var n = t.return; try { Bl(4, t) } catch (o) { Y(t, n, o) } break; case 1: var r = t.stateNode; if (typeof r.componentDidMount == "function") { var l = t.return; try { r.componentDidMount() } catch (o) { Y(t, l, o) } } var i = t.return; try { ys(t) } catch (o) { Y(t, i, o) } break; case 5: var a = t.return; try { ys(t) } catch (o) { Y(t, a, o) } } } catch (o) { Y(t, t.return, o) } if (t === e) { E = null; break } var u = t.sibling; if (u !== null) { u.return = t.return, E = u; break } E = t.return } } var Hm = Math.ceil, kl = rt.ReactCurrentDispatcher, da = rt.ReactCurrentOwner, be = rt.ReactCurrentBatchConfig, O = 0, te = null, K = null, re = 0, we = 0, un = Ct(0), J = 0, xr = null, Ht = 0, Hl = 0, fa = 0, er = null, me = null, ma = 0, jn = 1 / 0, qe = null, Pl = !1, ws = null, xt = null, Vr = !1, ft = null, Cl = 0, tr = 0, Ns = null, nl = -1, rl = 0; function ce() { return O & 6 ? G() : nl !== -1 ? nl : nl = G() } function wt(e) { return e.mode & 1 ? O & 2 && re !== 0 ? re & -re : Em.transition !== null ? (rl === 0 && (rl = Du()), rl) : (e = I, e !== 0 || (e = window.event, e = e === void 0 ? 16 : $u(e.type)), e) : 1 } function ze(e, t, n, r) { if (50 < tr) throw tr = 0, Ns = null, Error(S(185)); kr(e, n, r), (!(O & 2) || e !== te) && (e === te && (!(O & 2) && (Hl |= n), J === 4 && ct(e, re)), ve(e, r), n === 1 && O === 0 && !(t.mode & 1) && (jn = G() + 500, Ul && Et())) } function ve(e, t) { var n = e.callbackNode; Ef(e, t); var r = ul(e, e === te ? re : 0); if (r === 0) n !== null && Fa(n), e.callbackNode = null, e.callbackPriority = 0; else if (t = r & -r, e.callbackPriority !== t) { if (n != null && Fa(n), t === 1) e.tag === 0 ? Cm(To.bind(null, e)) : lc(To.bind(null, e)), jm(function () { !(O & 6) && Et() }), n = null; else { switch (Ru(r)) { case 1: n = $s; break; case 4: n = Mu; break; case 16: n = ol; break; case 536870912: n = Tu; break; default: n = ol }n = nd(n, Gc.bind(null, e)) } e.callbackPriority = t, e.callbackNode = n } } function Gc(e, t) { if (nl = -1, rl = 0, O & 6) throw Error(S(327)); var n = e.callbackNode; if (hn() && e.callbackNode !== n) return null; var r = ul(e, e === te ? re : 0); if (r === 0) return null; if (r & 30 || r & e.expiredLanes || t) t = El(e, r); else { t = r; var l = O; O |= 2; var i = Xc(); (te !== e || re !== t) && (qe = null, jn = G() + 500, Ft(e, t)); do try { Ym(); break } catch (u) { Kc(e, u) } while (!0); Js(), kl.current = i, O = l, K !== null ? t = 0 : (te = null, re = 0, t = J) } if (t !== 0) { if (t === 2 && (l = qi(e), l !== 0 && (r = l, t = js(e, l))), t === 1) throw n = xr, Ft(e, 0), ct(e, r), ve(e, G()), n; if (t === 6) ct(e, r); else { if (l = e.current.alternate, !(r & 30) && !Vm(l) && (t = El(e, r), t === 2 && (i = qi(e), i !== 0 && (r = i, t = js(e, i))), t === 1)) throw n = xr, Ft(e, 0), ct(e, r), ve(e, G()), n; switch (e.finishedWork = l, e.finishedLanes = r, t) { case 0: case 1: throw Error(S(345)); case 2: Dt(e, me, qe); break; case 3: if (ct(e, r), (r & 130023424) === r && (t = ma + 500 - G(), 10 < t)) { if (ul(e, 0) !== 0) break; if (l = e.suspendedLanes, (l & r) !== r) { ce(), e.pingedLanes |= e.suspendedLanes & l; break } e.timeoutHandle = ns(Dt.bind(null, e, me, qe), t); break } Dt(e, me, qe); break; case 4: if (ct(e, r), (r & 4194240) === r) break; for (t = e.eventTimes, l = -1; 0 < r;) { var a = 31 - Ie(r); i = 1 << a, a = t[a], a > l && (l = a), r &= ~i } if (r = l, r = G() - r, r = (120 > r ? 120 : 480 > r ? 480 : 1080 > r ? 1080 : 1920 > r ? 1920 : 3e3 > r ? 3e3 : 4320 > r ? 4320 : 1960 * Hm(r / 1960)) - r, 10 < r) { e.timeoutHandle = ns(Dt.bind(null, e, me, qe), r); break } Dt(e, me, qe); break; case 5: Dt(e, me, qe); break; default: throw Error(S(329)) } } } return ve(e, G()), e.callbackNode === n ? Gc.bind(null, e) : null } function js(e, t) { var n = er; return e.current.memoizedState.isDehydrated && (Ft(e, t).flags |= 256), e = El(e, t), e !== 2 && (t = me, me = n, t !== null && Ss(t)), e } function Ss(e) { me === null ? me = e : me.push.apply(me, e) } function Vm(e) { for (var t = e; ;) { if (t.flags & 16384) { var n = t.updateQueue; if (n !== null && (n = n.stores, n !== null)) for (var r = 0; r < n.length; r++) { var l = n[r], i = l.getSnapshot; l = l.value; try { if (!Fe(i(), l)) return !1 } catch { return !1 } } } if (n = t.child, t.subtreeFlags & 16384 && n !== null) n.return = t, t = n; else { if (t === e) break; for (; t.sibling === null;) { if (t.return === null || t.return === e) return !0; t = t.return } t.sibling.return = t.return, t = t.sibling } } return !0 } function ct(e, t) { for (t &= ~fa, t &= ~Hl, e.suspendedLanes |= t, e.pingedLanes &= ~t, e = e.expirationTimes; 0 < t;) { var n = 31 - Ie(t), r = 1 << n; e[n] = -1, t &= ~r } } function To(e) { if (O & 6) throw Error(S(327)); hn(); var t = ul(e, 0); if (!(t & 1)) return ve(e, G()), null; var n = El(e, t); if (e.tag !== 0 && n === 2) { var r = qi(e); r !== 0 && (t = r, n = js(e, r)) } if (n === 1) throw n = xr, Ft(e, 0), ct(e, t), ve(e, G()), n; if (n === 6) throw Error(S(345)); return e.finishedWork = e.current.alternate, e.finishedLanes = t, Dt(e, me, qe), ve(e, G()), null } function pa(e, t) { var n = O; O |= 1; try { return e(t) } finally { O = n, O === 0 && (jn = G() + 500, Ul && Et()) } } function Vt(e) { ft !== null && ft.tag === 0 && !(O & 6) && hn(); var t = O; O |= 1; var n = be.transition, r = I; try { if (be.transition = null, I = 1, e) return e() } finally { I = r, be.transition = n, O = t, !(O & 6) && Et() } } function ha() { we = un.current, $(un) } function Ft(e, t) { e.finishedWork = null, e.finishedLanes = 0; var n = e.timeoutHandle; if (n !== -1 && (e.timeoutHandle = -1, Nm(n)), K !== null) for (n = K.return; n !== null;) { var r = n; switch (Gs(r), r.tag) { case 1: r = r.type.childContextTypes, r != null && pl(); break; case 3: wn(), $(ge), $(oe), la(); break; case 5: ra(r); break; case 4: wn(); break; case 13: $(B); break; case 19: $(B); break; case 10: Zs(r.type._context); break; case 22: case 23: ha() }n = n.return } if (te = e, K = e = Nt(e.current, null), re = we = t, J = 0, xr = null, fa = Hl = Ht = 0, me = er = null, It !== null) { for (t = 0; t < It.length; t++)if (n = It[t], r = n.interleaved, r !== null) { n.interleaved = null; var l = r.next, i = n.pending; if (i !== null) { var a = i.next; i.next = l, r.next = a } n.pending = r } It = null } return e } function Kc(e, t) { do { var n = K; try { if (Js(), Zr.current = Sl, jl) { for (var r = H.memoizedState; r !== null;) { var l = r.queue; l !== null && (l.pending = null), r = r.next } jl = !1 } if (Bt = 0, ee = X = H = null, Jn = !1, gr = 0, da.current = null, n === null || n.return === null) { J = 1, xr = t, K = null; break } e: { var i = e, a = n.return, u = n, o = t; if (t = re, u.flags |= 32768, o !== null && typeof o == "object" && typeof o.then == "function") { var c = o, g = u, m = g.tag; if (!(g.mode & 1) && (m === 0 || m === 11 || m === 15)) { var h = g.alternate; h ? (g.updateQueue = h.updateQueue, g.memoizedState = h.memoizedState, g.lanes = h.lanes) : (g.updateQueue = null, g.memoizedState = null) } var w = vo(a); if (w !== null) { w.flags &= -257, xo(w, a, u, i, t), w.mode & 1 && yo(i, c, t), t = w, o = c; var y = t.updateQueue; if (y === null) { var x = new Set; x.add(o), t.updateQueue = x } else y.add(o); break e } else { if (!(t & 1)) { yo(i, c, t), ga(); break e } o = Error(S(426)) } } else if (A && u.mode & 1) { var k = vo(a); if (k !== null) { !(k.flags & 65536) && (k.flags |= 256), xo(k, a, u, i, t), Ks(Nn(o, u)); break e } } i = o = Nn(o, u), J !== 4 && (J = 2), er === null ? er = [i] : er.push(i), i = a; do { switch (i.tag) { case 3: i.flags |= 65536, t &= -t, i.lanes |= t; var f = Rc(i, o, t); co(i, f); break e; case 1: u = o; var d = i.type, p = i.stateNode; if (!(i.flags & 128) && (typeof d.getDerivedStateFromError == "function" || p !== null && typeof p.componentDidCatch == "function" && (xt === null || !xt.has(p)))) { i.flags |= 65536, t &= -t, i.lanes |= t; var v = Oc(i, u, t); co(i, v); break e } }i = i.return } while (i !== null) } Zc(n) } catch (j) { t = j, K === n && n !== null && (K = n = n.return); continue } break } while (!0) } function Xc() { var e = kl.current; return kl.current = Sl, e === null ? Sl : e } function ga() { (J === 0 || J === 3 || J === 2) && (J = 4), te === null || !(Ht & 268435455) && !(Hl & 268435455) || ct(te, re) } function El(e, t) { var n = O; O |= 2; var r = Xc(); (te !== e || re !== t) && (qe = null, Ft(e, t)); do try { Qm(); break } catch (l) { Kc(e, l) } while (!0); if (Js(), O = n, kl.current = r, K !== null) throw Error(S(261)); return te = null, re = 0, J } function Qm() { for (; K !== null;)Jc(K) } function Ym() { for (; K !== null && !vf();)Jc(K) } function Jc(e) { var t = td(e.alternate, e, we); e.memoizedProps = e.pendingProps, t === null ? Zc(e) : K = t, da.current = null } function Zc(e) { var t = e; do { var n = t.alternate; if (e = t.return, t.flags & 32768) { if (n = Um(n, t), n !== null) { n.flags &= 32767, K = n; return } if (e !== null) e.flags |= 32768, e.subtreeFlags = 0, e.deletions = null; else { J = 6, K = null; return } } else if (n = $m(n, t, we), n !== null) { K = n; return } if (t = t.sibling, t !== null) { K = t; return } K = t = e } while (t !== null); J === 0 && (J = 5) } function Dt(e, t, n) { var r = I, l = be.transition; try { be.transition = null, I = 1, qm(e, t, n, r) } finally { be.transition = l, I = r } return null } function qm(e, t, n, r) { do hn(); while (ft !== null); if (O & 6) throw Error(S(327)); n = e.finishedWork; var l = e.finishedLanes; if (n === null) return null; if (e.finishedWork = null, e.finishedLanes = 0, n === e.current) throw Error(S(177)); e.callbackNode = null, e.callbackPriority = 0; var i = n.lanes | n.childLanes; if (_f(e, i), e === te && (K = te = null, re = 0), !(n.subtreeFlags & 2064) && !(n.flags & 2064) || Vr || (Vr = !0, nd(ol, function () { return hn(), null })), i = (n.flags & 15990) !== 0, n.subtreeFlags & 15990 || i) { i = be.transition, be.transition = null; var a = I; I = 1; var u = O; O |= 4, da.current = null, Wm(e, n), Yc(n, e), pm(es), cl = !!Zi, es = Zi = null, e.current = n, Bm(n), xf(), O = u, I = a, be.transition = i } else e.current = n; if (Vr && (Vr = !1, ft = e, Cl = l), i = e.pendingLanes, i === 0 && (xt = null), jf(n.stateNode), ve(e, G()), t !== null) for (r = e.onRecoverableError, n = 0; n < t.length; n++)l = t[n], r(l.value, { componentStack: l.stack, digest: l.digest }); if (Pl) throw Pl = !1, e = ws, ws = null, e; return Cl & 1 && e.tag !== 0 && hn(), i = e.pendingLanes, i & 1 ? e === Ns ? tr++ : (tr = 0, Ns = e) : tr = 0, Et(), null } function hn() { if (ft !== null) { var e = Ru(Cl), t = be.transition, n = I; try { if (be.transition = null, I = 16 > e ? 16 : e, ft === null) var r = !1; else { if (e = ft, ft = null, Cl = 0, O & 6) throw Error(S(331)); var l = O; for (O |= 4, E = e.current; E !== null;) { var i = E, a = i.child; if (E.flags & 16) { var u = i.deletions; if (u !== null) { for (var o = 0; o < u.length; o++) { var c = u[o]; for (E = c; E !== null;) { var g = E; switch (g.tag) { case 0: case 11: case 15: Zn(8, g, i) }var m = g.child; if (m !== null) m.return = g, E = m; else for (; E !== null;) { g = E; var h = g.sibling, w = g.return; if (Hc(g), g === c) { E = null; break } if (h !== null) { h.return = w, E = h; break } E = w } } } var y = i.alternate; if (y !== null) { var x = y.child; if (x !== null) { y.child = null; do { var k = x.sibling; x.sibling = null, x = k } while (x !== null) } } E = i } } if (i.subtreeFlags & 2064 && a !== null) a.return = i, E = a; else e: for (; E !== null;) { if (i = E, i.flags & 2048) switch (i.tag) { case 0: case 11: case 15: Zn(9, i, i.return) }var f = i.sibling; if (f !== null) { f.return = i.return, E = f; break e } E = i.return } } var d = e.current; for (E = d; E !== null;) { a = E; var p = a.child; if (a.subtreeFlags & 2064 && p !== null) p.return = a, E = p; else e: for (a = d; E !== null;) { if (u = E, u.flags & 2048) try { switch (u.tag) { case 0: case 11: case 15: Bl(9, u) } } catch (j) { Y(u, u.return, j) } if (u === a) { E = null; break e } var v = u.sibling; if (v !== null) { v.return = u.return, E = v; break e } E = u.return } } if (O = l, Et(), Ve && typeof Ve.onPostCommitFiberRoot == "function") try { Ve.onPostCommitFiberRoot(Ll, e) } catch { } r = !0 } return r } finally { I = n, be.transition = t } } return !1 } function Do(e, t, n) { t = Nn(n, t), t = Rc(e, t, 1), e = vt(e, t, 1), t = ce(), e !== null && (kr(e, 1, t), ve(e, t)) } function Y(e, t, n) { if (e.tag === 3) Do(e, e, n); else for (; t !== null;) { if (t.tag === 3) { Do(t, e, n); break } else if (t.tag === 1) { var r = t.stateNode; if (typeof t.type.getDerivedStateFromError == "function" || typeof r.componentDidCatch == "function" && (xt === null || !xt.has(r))) { e = Nn(n, e), e = Oc(t, e, 1), t = vt(t, e, 1), e = ce(), t !== null && (kr(t, 1, e), ve(t, e)); break } } t = t.return } } function Gm(e, t, n) { var r = e.pingCache; r !== null && r.delete(t), t = ce(), e.pingedLanes |= e.suspendedLanes & n, te === e && (re & n) === n && (J === 4 || J === 3 && (re & 130023424) === re && 500 > G() - ma ? Ft(e, 0) : fa |= n), ve(e, t) } function ed(e, t) { t === 0 && (e.mode & 1 ? (t = Lr, Lr <<= 1, !(Lr & 130023424) && (Lr = 4194304)) : t = 1); var n = ce(); e = tt(e, t), e !== null && (kr(e, t, n), ve(e, n)) } function Km(e) { var t = e.memoizedState, n = 0; t !== null && (n = t.retryLane), ed(e, n) } function Xm(e, t) { var n = 0; switch (e.tag) { case 13: var r = e.stateNode, l = e.memoizedState; l !== null && (n = l.retryLane); break; case 19: r = e.stateNode; break; default: throw Error(S(314)) }r !== null && r.delete(t), ed(e, n) } var td; td = function (e, t, n) { if (e !== null) if (e.memoizedProps !== t.pendingProps || ge.current) he = !0; else { if (!(e.lanes & n) && !(t.flags & 128)) return he = !1, Fm(e, t, n); he = !!(e.flags & 131072) } else he = !1, A && t.flags & 1048576 && ic(t, yl, t.index); switch (t.lanes = 0, t.tag) { case 2: var r = t.type; tl(e, t), e = t.pendingProps; var l = yn(t, oe.current); pn(t, n), l = sa(null, t, r, e, l, n); var i = aa(); return t.flags |= 1, typeof l == "object" && l !== null && typeof l.render == "function" && l.$$typeof === void 0 ? (t.tag = 1, t.memoizedState = null, t.updateQueue = null, ye(r) ? (i = !0, hl(t)) : i = !1, t.memoizedState = l.state !== null && l.state !== void 0 ? l.state : null, ta(t), l.updater = Wl, t.stateNode = l, l._reactInternals = t, us(t, r, e, n), t = fs(null, t, r, !0, i, n)) : (t.tag = 0, A && i && qs(t), ue(null, t, l, n), t = t.child), t; case 16: r = t.elementType; e: { switch (tl(e, t), e = t.pendingProps, l = r._init, r = l(r._payload), t.type = r, l = t.tag = Zm(r), e = Re(r, e), l) { case 0: t = ds(null, t, r, e, n); break e; case 1: t = jo(null, t, r, e, n); break e; case 11: t = wo(null, t, r, e, n); break e; case 14: t = No(null, t, r, Re(r.type, e), n); break e }throw Error(S(306, r, "")) } return t; case 0: return r = t.type, l = t.pendingProps, l = t.elementType === r ? l : Re(r, l), ds(e, t, r, l, n); case 1: return r = t.type, l = t.pendingProps, l = t.elementType === r ? l : Re(r, l), jo(e, t, r, l, n); case 3: e: { if (Fc(t), e === null) throw Error(S(387)); r = t.pendingProps, i = t.memoizedState, l = i.element, dc(e, t), wl(t, r, null, n); var a = t.memoizedState; if (r = a.element, i.isDehydrated) if (i = { element: r, isDehydrated: !1, cache: a.cache, pendingSuspenseBoundaries: a.pendingSuspenseBoundaries, transitions: a.transitions }, t.updateQueue.baseState = i, t.memoizedState = i, t.flags & 256) { l = Nn(Error(S(423)), t), t = So(e, t, r, n, l); break e } else if (r !== l) { l = Nn(Error(S(424)), t), t = So(e, t, r, n, l); break e } else for (Ne = yt(t.stateNode.containerInfo.firstChild), je = t, A = !0, Le = null, n = uc(t, null, r, n), t.child = n; n;)n.flags = n.flags & -3 | 4096, n = n.sibling; else { if (vn(), r === l) { t = nt(e, t, n); break e } ue(e, t, r, n) } t = t.child } return t; case 5: return fc(t), e === null && ss(t), r = t.type, l = t.pendingProps, i = e !== null ? e.memoizedProps : null, a = l.children, ts(r, l) ? a = null : i !== null && ts(r, i) && (t.flags |= 32), zc(e, t), ue(e, t, a, n), t.child; case 6: return e === null && ss(t), null; case 13: return $c(e, t, n); case 4: return na(t, t.stateNode.containerInfo), r = t.pendingProps, e === null ? t.child = xn(t, null, r, n) : ue(e, t, r, n), t.child; case 11: return r = t.type, l = t.pendingProps, l = t.elementType === r ? l : Re(r, l), wo(e, t, r, l, n); case 7: return ue(e, t, t.pendingProps, n), t.child; case 8: return ue(e, t, t.pendingProps.children, n), t.child; case 12: return ue(e, t, t.pendingProps.children, n), t.child; case 10: e: { if (r = t.type._context, l = t.pendingProps, i = t.memoizedProps, a = l.value, z(vl, r._currentValue), r._currentValue = a, i !== null) if (Fe(i.value, a)) { if (i.children === l.children && !ge.current) { t = nt(e, t, n); break e } } else for (i = t.child, i !== null && (i.return = t); i !== null;) { var u = i.dependencies; if (u !== null) { a = i.child; for (var o = u.firstContext; o !== null;) { if (o.context === r) { if (i.tag === 1) { o = Je(-1, n & -n), o.tag = 2; var c = i.updateQueue; if (c !== null) { c = c.shared; var g = c.pending; g === null ? o.next = o : (o.next = g.next, g.next = o), c.pending = o } } i.lanes |= n, o = i.alternate, o !== null && (o.lanes |= n), as(i.return, n, t), u.lanes |= n; break } o = o.next } } else if (i.tag === 10) a = i.type === t.type ? null : i.child; else if (i.tag === 18) { if (a = i.return, a === null) throw Error(S(341)); a.lanes |= n, u = a.alternate, u !== null && (u.lanes |= n), as(a, n, t), a = i.sibling } else a = i.child; if (a !== null) a.return = i; else for (a = i; a !== null;) { if (a === t) { a = null; break } if (i = a.sibling, i !== null) { i.return = a.return, a = i; break } a = a.return } i = a } ue(e, t, l.children, n), t = t.child } return t; case 9: return l = t.type, r = t.pendingProps.children, pn(t, n), l = Me(l), r = r(l), t.flags |= 1, ue(e, t, r, n), t.child; case 14: return r = t.type, l = Re(r, t.pendingProps), l = Re(r.type, l), No(e, t, r, l, n); case 15: return Lc(e, t, t.type, t.pendingProps, n); case 17: return r = t.type, l = t.pendingProps, l = t.elementType === r ? l : Re(r, l), tl(e, t), t.tag = 1, ye(r) ? (e = !0, hl(t)) : e = !1, pn(t, n), Dc(t, r, l), us(t, r, l, n), fs(null, t, r, !0, e, n); case 19: return Uc(e, t, n); case 22: return Ic(e, t, n) }throw Error(S(156, t.tag)) }; function nd(e, t) { return bu(e, t) } function Jm(e, t, n, r) { this.tag = e, this.key = n, this.sibling = this.child = this.return = this.stateNode = this.type = this.elementType = null, this.index = 0, this.ref = null, this.pendingProps = t, this.dependencies = this.memoizedState = this.updateQueue = this.memoizedProps = null, this.mode = r, this.subtreeFlags = this.flags = 0, this.deletions = null, this.childLanes = this.lanes = 0, this.alternate = null } function _e(e, t, n, r) { return new Jm(e, t, n, r) } function ya(e) { return e = e.prototype, !(!e || !e.isReactComponent) } function Zm(e) { if (typeof e == "function") return ya(e) ? 1 : 0; if (e != null) { if (e = e.$$typeof, e === Is) return 11; if (e === zs) return 14 } return 2 } function Nt(e, t) { var n = e.alternate; return n === null ? (n = _e(e.tag, t, e.key, e.mode), n.elementType = e.elementType, n.type = e.type, n.stateNode = e.stateNode, n.alternate = e, e.alternate = n) : (n.pendingProps = t, n.type = e.type, n.flags = 0, n.subtreeFlags = 0, n.deletions = null), n.flags = e.flags & 14680064, n.childLanes = e.childLanes, n.lanes = e.lanes, n.child = e.child, n.memoizedProps = e.memoizedProps, n.memoizedState = e.memoizedState, n.updateQueue = e.updateQueue, t = e.dependencies, n.dependencies = t === null ? null : { lanes: t.lanes, firstContext: t.firstContext }, n.sibling = e.sibling, n.index = e.index, n.ref = e.ref, n } function ll(e, t, n, r, l, i) { var a = 2; if (r = e, typeof e == "function") ya(e) && (a = 1); else if (typeof e == "string") a = 5; else e: switch (e) { case Jt: return $t(n.children, l, i, t); case Ls: a = 8, l |= 8; break; case Ri: return e = _e(12, n, t, l | 2), e.elementType = Ri, e.lanes = i, e; case Oi: return e = _e(13, n, t, l), e.elementType = Oi, e.lanes = i, e; case Li: return e = _e(19, n, t, l), e.elementType = Li, e.lanes = i, e; case fu: return Vl(n, l, i, t); default: if (typeof e == "object" && e !== null) switch (e.$$typeof) { case cu: a = 10; break e; case du: a = 9; break e; case Is: a = 11; break e; case zs: a = 14; break e; case at: a = 16, r = null; break e }throw Error(S(130, e == null ? e : typeof e, "")) }return t = _e(a, n, t, l), t.elementType = e, t.type = r, t.lanes = i, t } function $t(e, t, n, r) { return e = _e(7, e, r, t), e.lanes = n, e } function Vl(e, t, n, r) { return e = _e(22, e, r, t), e.elementType = fu, e.lanes = n, e.stateNode = { isHidden: !1 }, e } function _i(e, t, n) { return e = _e(6, e, null, t), e.lanes = n, e } function bi(e, t, n) { return t = _e(4, e.children !== null ? e.children : [], e.key, t), t.lanes = n, t.stateNode = { containerInfo: e.containerInfo, pendingChildren: null, implementation: e.implementation }, t } function ep(e, t, n, r, l) { this.tag = t, this.containerInfo = e, this.finishedWork = this.pingCache = this.current = this.pendingChildren = null, this.timeoutHandle = -1, this.callbackNode = this.pendingContext = this.context = null, this.callbackPriority = 0, this.eventTimes = ui(0), this.expirationTimes = ui(-1), this.entangledLanes = this.finishedLanes = this.mutableReadLanes = this.expiredLanes = this.pingedLanes = this.suspendedLanes = this.pendingLanes = 0, this.entanglements = ui(0), this.identifierPrefix = r, this.onRecoverableError = l, this.mutableSourceEagerHydrationData = null } function va(e, t, n, r, l, i, a, u, o) { return e = new ep(e, t, n, u, o), t === 1 ? (t = 1, i === !0 && (t |= 8)) : t = 0, i = _e(3, null, null, t), e.current = i, i.stateNode = e, i.memoizedState = { element: r, isDehydrated: n, cache: null, transitions: null, pendingSuspenseBoundaries: null }, ta(i), e } function tp(e, t, n) { var r = 3 < arguments.length && arguments[3] !== void 0 ? arguments[3] : null; return { $$typeof: Xt, key: r == null ? null : "" + r, children: e, containerInfo: t, implementation: n } } function rd(e) { if (!e) return kt; e = e._reactInternals; e: { if (qt(e) !== e || e.tag !== 1) throw Error(S(170)); var t = e; do { switch (t.tag) { case 3: t = t.stateNode.context; break e; case 1: if (ye(t.type)) { t = t.stateNode.__reactInternalMemoizedMergedChildContext; break e } }t = t.return } while (t !== null); throw Error(S(171)) } if (e.tag === 1) { var n = e.type; if (ye(n)) return rc(e, n, t) } return t } function ld(e, t, n, r, l, i, a, u, o) { return e = va(n, r, !0, e, l, i, a, u, o), e.context = rd(null), n = e.current, r = ce(), l = wt(n), i = Je(r, l), i.callback = t ?? null, vt(n, i, l), e.current.lanes = l, kr(e, l, r), ve(e, r), e } function Ql(e, t, n, r) { var l = t.current, i = ce(), a = wt(l); return n = rd(n), t.context === null ? t.context = n : t.pendingContext = n, t = Je(i, a), t.payload = { element: e }, r = r === void 0 ? null : r, r !== null && (t.callback = r), e = vt(l, t, a), e !== null && (ze(e, l, a, i), Jr(e, l, a)), a } function _l(e) { if (e = e.current, !e.child) return null; switch (e.child.tag) { case 5: return e.child.stateNode; default: return e.child.stateNode } } function Ro(e, t) { if (e = e.memoizedState, e !== null && e.dehydrated !== null) { var n = e.retryLane; e.retryLane = n !== 0 && n < t ? n : t } } function xa(e, t) { Ro(e, t), (e = e.alternate) && Ro(e, t) } function np() { return null } var id = typeof reportError == "function" ? reportError : function (e) { console.error(e) }; function wa(e) { this._internalRoot = e } Yl.prototype.render = wa.prototype.render = function (e) { var t = this._internalRoot; if (t === null) throw Error(S(409)); Ql(e, t, null, null) }; Yl.prototype.unmount = wa.prototype.unmount = function () { var e = this._internalRoot; if (e !== null) { this._internalRoot = null; var t = e.containerInfo; Vt(function () { Ql(null, e, null, null) }), t[et] = null } }; function Yl(e) { this._internalRoot = e } Yl.prototype.unstable_scheduleHydration = function (e) { if (e) { var t = Iu(); e = { blockedOn: null, target: e, priority: t }; for (var n = 0; n < ut.length && t !== 0 && t < ut[n].priority; n++); ut.splice(n, 0, e), n === 0 && Fu(e) } }; function Na(e) { return !(!e || e.nodeType !== 1 && e.nodeType !== 9 && e.nodeType !== 11) } function ql(e) { return !(!e || e.nodeType !== 1 && e.nodeType !== 9 && e.nodeType !== 11 && (e.nodeType !== 8 || e.nodeValue !== " react-mount-point-unstable ")) } function Oo() { } function rp(e, t, n, r, l) { if (l) { if (typeof r == "function") { var i = r; r = function () { var c = _l(a); i.call(c) } } var a = ld(t, r, e, 0, null, !1, !1, "", Oo); return e._reactRootContainer = a, e[et] = a.current, dr(e.nodeType === 8 ? e.parentNode : e), Vt(), a } for (; l = e.lastChild;)e.removeChild(l); if (typeof r == "function") { var u = r; r = function () { var c = _l(o); u.call(c) } } var o = va(e, 0, !1, null, null, !1, !1, "", Oo); return e._reactRootContainer = o, e[et] = o.current, dr(e.nodeType === 8 ? e.parentNode : e), Vt(function () { Ql(t, o, n, r) }), o } function Gl(e, t, n, r, l) { var i = n._reactRootContainer; if (i) { var a = i; if (typeof l == "function") { var u = l; l = function () { var o = _l(a); u.call(o) } } Ql(t, a, e, l) } else a = rp(n, t, e, l, r); return _l(a) } Ou = function (e) { switch (e.tag) { case 3: var t = e.stateNode; if (t.current.memoizedState.isDehydrated) { var n = Vn(t.pendingLanes); n !== 0 && (Us(t, n | 1), ve(t, G()), !(O & 6) && (jn = G() + 500, Et())) } break; case 13: Vt(function () { var r = tt(e, 1); if (r !== null) { var l = ce(); ze(r, e, 1, l) } }), xa(e, 1) } }; As = function (e) { if (e.tag === 13) { var t = tt(e, 134217728); if (t !== null) { var n = ce(); ze(t, e, 134217728, n) } xa(e, 134217728) } }; Lu = function (e) { if (e.tag === 13) { var t = wt(e), n = tt(e, t); if (n !== null) { var r = ce(); ze(n, e, t, r) } xa(e, t) } }; Iu = function () { return I }; zu = function (e, t) { var n = I; try { return I = e, t() } finally { I = n } }; Vi = function (e, t, n) { switch (t) { case "input": if (Fi(e, n), t = n.name, n.type === "radio" && t != null) { for (n = e; n.parentNode;)n = n.parentNode; for (n = n.querySelectorAll("input[name=" + JSON.stringify("" + t) + '][type="radio"]'), t = 0; t < n.length; t++) { var r = n[t]; if (r !== e && r.form === e.form) { var l = $l(r); if (!l) throw Error(S(90)); pu(r), Fi(r, l) } } } break; case "textarea": gu(e, n); break; case "select": t = n.value, t != null && cn(e, !!n.multiple, t, !1) } }; Su = pa; ku = Vt; var lp = { usingClientEntryPoint: !1, Events: [Cr, nn, $l, Nu, ju, pa] }, Un = { findFiberByHostInstance: Lt, bundleType: 0, version: "18.3.1", rendererPackageName: "react-dom" }, ip = { bundleType: Un.bundleType, version: Un.version, rendererPackageName: Un.rendererPackageName, rendererConfig: Un.rendererConfig, overrideHookState: null, overrideHookStateDeletePath: null, overrideHookStateRenamePath: null, overrideProps: null, overridePropsDeletePath: null, overridePropsRenamePath: null, setErrorHandler: null, setSuspenseHandler: null, scheduleUpdate: null, currentDispatcherRef: rt.ReactCurrentDispatcher, findHostInstanceByFiber: function (e) { return e = Eu(e), e === null ? null : e.stateNode }, findFiberByHostInstance: Un.findFiberByHostInstance || np, findHostInstancesForRefresh: null, scheduleRefresh: null, scheduleRoot: null, setRefreshHandler: null, getCurrentFiber: null, reconcilerVersion: "18.3.1-next-f1338f8080-20240426" }; if (typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ < "u") { var Qr = __REACT_DEVTOOLS_GLOBAL_HOOK__; if (!Qr.isDisabled && Qr.supportsFiber) try { Ll = Qr.inject(ip), Ve = Qr } catch { } } ke.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = lp; ke.createPortal = function (e, t) { var n = 2 < arguments.length && arguments[2] !== void 0 ? arguments[2] : null; if (!Na(t)) throw Error(S(200)); return tp(e, t, null, n) }; ke.createRoot = function (e, t) { if (!Na(e)) throw Error(S(299)); var n = !1, r = "", l = id; return t != null && (t.unstable_strictMode === !0 && (n = !0), t.identifierPrefix !== void 0 && (r = t.identifierPrefix), t.onRecoverableError !== void 0 && (l = t.onRecoverableError)), t = va(e, 1, !1, null, null, n, !1, r, l), e[et] = t.current, dr(e.nodeType === 8 ? e.parentNode : e), new wa(t) }; ke.findDOMNode = function (e) { if (e == null) return null; if (e.nodeType === 1) return e; var t = e._reactInternals; if (t === void 0) throw typeof e.render == "function" ? Error(S(188)) : (e = Object.keys(e).join(","), Error(S(268, e))); return e = Eu(t), e = e === null ? null : e.stateNode, e }; ke.flushSync = function (e) { return Vt(e) }; ke.hydrate = function (e, t, n) { if (!ql(t)) throw Error(S(200)); return Gl(null, e, t, !0, n) }; ke.hydrateRoot = function (e, t, n) { if (!Na(e)) throw Error(S(405)); var r = n != null && n.hydratedSources || null, l = !1, i = "", a = id; if (n != null && (n.unstable_strictMode === !0 && (l = !0), n.identifierPrefix !== void 0 && (i = n.identifierPrefix), n.onRecoverableError !== void 0 && (a = n.onRecoverableError)), t = ld(t, null, e, 1, n ?? null, l, !1, i, a), e[et] = t.current, dr(e), r) for (e = 0; e < r.length; e++)n = r[e], l = n._getVersion, l = l(n._source), t.mutableSourceEagerHydrationData == null ? t.mutableSourceEagerHydrationData = [n, l] : t.mutableSourceEagerHydrationData.push(n, l); return new Yl(t) }; ke.render = function (e, t, n) { if (!ql(t)) throw Error(S(200)); return Gl(null, e, t, !1, n) }; ke.unmountComponentAtNode = function (e) { if (!ql(e)) throw Error(S(40)); return e._reactRootContainer ? (Vt(function () { Gl(null, null, e, !1, function () { e._reactRootContainer = null, e[et] = null }) }), !0) : !1 }; ke.unstable_batchedUpdates = pa; ke.unstable_renderSubtreeIntoContainer = function (e, t, n, r) { if (!ql(n)) throw Error(S(200)); if (e == null || e._reactInternals === void 0) throw Error(S(38)); return Gl(e, t, n, !1, r) }; ke.version = "18.3.1-next-f1338f8080-20240426"; function sd() { if (!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ > "u" || typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE != "function")) try { __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(sd) } catch (e) { console.error(e) } } sd(), su.exports = ke; var sp = su.exports, ad, Lo = sp; ad = Lo.createRoot, Lo.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function wr() { return wr = Object.assign ? Object.assign.bind() : function (e) { for (var t = 1; t < arguments.length; t++) { var n = arguments[t]; for (var r in n) Object.prototype.hasOwnProperty.call(n, r) && (e[r] = n[r]) } return e }, wr.apply(this, arguments) } var mt; (function (e) { e.Pop = "POP", e.Push = "PUSH", e.Replace = "REPLACE" })(mt || (mt = {})); const Io = "popstate"; function ap(e) { e === void 0 && (e = {}); function t(r, l) { let { pathname: i, search: a, hash: u } = r.location; return ks("", { pathname: i, search: a, hash: u }, l.state && l.state.usr || null, l.state && l.state.key || "default") } function n(r, l) { return typeof l == "string" ? l : bl(l) } return up(t, n, null, e) } function V(e, t) { if (e === !1 || e === null || typeof e > "u") throw new Error(t) } function od(e, t) { if (!e) { typeof console < "u" && console.warn(t); try { throw new Error(t) } catch { } } } function op() { return Math.random().toString(36).substr(2, 8) } function zo(e, t) { return { usr: e.state, key: e.key, idx: t } } function ks(e, t, n, r) { return n === void 0 && (n = null), wr({ pathname: typeof e == "string" ? e : e.pathname, search: "", hash: "" }, typeof t == "string" ? _n(t) : t, { state: n, key: t && t.key || r || op() }) } function bl(e) { let { pathname: t = "/", search: n = "", hash: r = "" } = e; return n && n !== "?" && (t += n.charAt(0) === "?" ? n : "?" + n), r && r !== "#" && (t += r.charAt(0) === "#" ? r : "#" + r), t } function _n(e) { let t = {}; if (e) { let n = e.indexOf("#"); n >= 0 && (t.hash = e.substr(n), e = e.substr(0, n)); let r = e.indexOf("?"); r >= 0 && (t.search = e.substr(r), e = e.substr(0, r)), e && (t.pathname = e) } return t } function up(e, t, n, r) { r === void 0 && (r = {}); let { window: l = document.defaultView, v5Compat: i = !1 } = r, a = l.history, u = mt.Pop, o = null, c = g(); c == null && (c = 0, a.replaceState(wr({}, a.state, { idx: c }), "")); function g() { return (a.state || { idx: null }).idx } function m() { u = mt.Pop; let k = g(), f = k == null ? null : k - c; c = k, o && o({ action: u, location: x.location, delta: f }) } function h(k, f) { u = mt.Push; let d = ks(x.location, k, f); c = g() + 1; let p = zo(d, c), v = x.createHref(d); try { a.pushState(p, "", v) } catch (j) { if (j instanceof DOMException && j.name === "DataCloneError") throw j; l.location.assign(v) } i && o && o({ action: u, location: x.location, delta: 1 }) } function w(k, f) { u = mt.Replace; let d = ks(x.location, k, f); c = g(); let p = zo(d, c), v = x.createHref(d); a.replaceState(p, "", v), i && o && o({ action: u, location: x.location, delta: 0 }) } function y(k) { let f = l.location.origin !== "null" ? l.location.origin : l.location.href, d = typeof k == "string" ? k : bl(k); return d = d.replace(/ $/, "%20"), V(f, "No window.location.(origin|href) available to create URL for href: " + d), new URL(d, f) } let x = { get action() { return u }, get location() { return e(l, a) }, listen(k) { if (o) throw new Error("A history only accepts one active listener"); return l.addEventListener(Io, m), o = k, () => { l.removeEventListener(Io, m), o = null } }, createHref(k) { return t(l, k) }, createURL: y, encodeLocation(k) { let f = y(k); return { pathname: f.pathname, search: f.search, hash: f.hash } }, push: h, replace: w, go(k) { return a.go(k) } }; return x } var Fo; (function (e) { e.data = "data", e.deferred = "deferred", e.redirect = "redirect", e.error = "error" })(Fo || (Fo = {})); function cp(e, t, n) { return n === void 0 && (n = "/"), dp(e, t, n, !1) } function dp(e, t, n, r) { let l = typeof t == "string" ? _n(t) : t, i = Sn(l.pathname || "/", n); if (i == null) return null; let a = ud(e); fp(a); let u = null; for (let o = 0; u == null && o < a.length; ++o) { let c = Sp(i); u = Np(a[o], c, r) } return u } function ud(e, t, n, r) { t === void 0 && (t = []), n === void 0 && (n = []), r === void 0 && (r = ""); let l = (i, a, u) => { let o = { relativePath: u === void 0 ? i.path || "" : u, caseSensitive: i.caseSensitive === !0, childrenIndex: a, route: i }; o.relativePath.startsWith("/") && (V(o.relativePath.startsWith(r), 'Absolute route path "' + o.relativePath + '" nested under path ' + ('"' + r + '" is not valid. An absolute child route path ') + "must start with the combined path of all its parent routes."), o.relativePath = o.relativePath.slice(r.length)); let c = jt([r, o.relativePath]), g = n.concat(o); i.children && i.children.length > 0 && (V(i.index !== !0, "Index routes must not have child routes. Please remove " + ('all child routes from route path "' + c + '".')), ud(i.children, t, g, c)), !(i.path == null && !i.index) && t.push({ path: c, score: xp(c, i.index), routesMeta: g }) }; return e.forEach((i, a) => { var u; if (i.path === "" || !((u = i.path) != null && u.includes("?"))) l(i, a); else for (let o of cd(i.path)) l(i, a, o) }), t } function cd(e) { let t = e.split("/"); if (t.length === 0) return []; let [n, ...r] = t, l = n.endsWith("?"), i = n.replace(/\?$/, ""); if (r.length === 0) return l ? [i, ""] : [i]; let a = cd(r.join("/")), u = []; return u.push(...a.map(o => o === "" ? i : [i, o].join("/"))), l && u.push(...a), u.map(o => e.startsWith("/") && o === "" ? "/" : o) } function fp(e) { e.sort((t, n) => t.score !== n.score ? n.score - t.score : wp(t.routesMeta.map(r => r.childrenIndex), n.routesMeta.map(r => r.childrenIndex))) } const mp = /^:[\w-]+$/, pp = 3, hp = 2, gp = 1, yp = 10, vp = -2, $o = e => e === "*"; function xp(e, t) { let n = e.split("/"), r = n.length; return n.some($o) && (r += vp), t && (r += hp), n.filter(l => !$o(l)).reduce((l, i) => l + (mp.test(i) ? pp : i === "" ? gp : yp), r) } function wp(e, t) { return e.length === t.length && e.slice(0, -1).every((r, l) => r === t[l]) ? e[e.length - 1] - t[t.length - 1] : 0 } function Np(e, t, n) { let { routesMeta: r } = e, l = {}, i = "/", a = []; for (let u = 0; u < r.length; ++u) { let o = r[u], c = u === r.length - 1, g = i === "/" ? t : t.slice(i.length) || "/", m = Ml({ path: o.relativePath, caseSensitive: o.caseSensitive, end: c }, g), h = o.route; if (!m && c && n && !r[r.length - 1].route.index && (m = Ml({ path: o.relativePath, caseSensitive: o.caseSensitive, end: !1 }, g)), !m) return null; Object.assign(l, m.params), a.push({ params: l, pathname: jt([i, m.pathname]), pathnameBase: Ep(jt([i, m.pathnameBase])), route: h }), m.pathnameBase !== "/" && (i = jt([i, m.pathnameBase])) } return a } function Ml(e, t) { typeof e == "string" && (e = { path: e, caseSensitive: !1, end: !0 }); let [n, r] = jp(e.path, e.caseSensitive, e.end), l = t.match(n); if (!l) return null; let i = l[0], a = i.replace(/(.)\/+$/, "$1"), u = l.slice(1); return { params: r.reduce((c, g, m) => { let { paramName: h, isOptional: w } = g; if (h === "*") { let x = u[m] || ""; a = i.slice(0, i.length - x.length).replace(/(.)\/+$/, "$1") } const y = u[m]; return w && !y ? c[h] = void 0 : c[h] = (y || "").replace(/%2F/g, "/"), c }, {}), pathname: i, pathnameBase: a, pattern: e } } function jp(e, t, n) { t === void 0 && (t = !1), n === void 0 && (n = !0), od(e === "*" || !e.endsWith("*") || e.endsWith("/*"), 'Route path "' + e + '" will be treated as if it were ' + ('"' + e.replace(/\*$/, "/*") + '" because the `*` character must ') + "always follow a `/` in the pattern. To get rid of this warning, " + ('please change the route path to "' + e.replace(/\*$/, "/*") + '".')); let r = [], l = "^" + e.replace(/\/*\*?$/, "").replace(/^\/*/, "/").replace(/[\\.*+^${}|()[\]]/g, "\\$&").replace(/\/:([\w-]+)(\?)?/g, (a, u, o) => (r.push({ paramName: u, isOptional: o != null }), o ? "/?([^\\/]+)?" : "/([^\\/]+)")); return e.endsWith("*") ? (r.push({ paramName: "*" }), l += e === "*" || e === "/*" ? "(.*)$" : "(?:\\/(.+)|\\/*)$") : n ? l += "\\/*$" : e !== "" && e !== "/" && (l += "(?:(?=\\/|$))"), [new RegExp(l, t ? void 0 : "i"), r] } function Sp(e) { try { return e.split("/").map(t => decodeURIComponent(t).replace(/\//g, "%2F")).join("/") } catch (t) { return od(!1, 'The URL path "' + e + '" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent ' + ("encoding (" + t + ").")), e } } function Sn(e, t) { if (t === "/") return e; if (!e.toLowerCase().startsWith(t.toLowerCase())) return null; let n = t.endsWith("/") ? t.length - 1 : t.length, r = e.charAt(n); return r && r !== "/" ? null : e.slice(n) || "/" } function kp(e, t) { t === void 0 && (t = "/"); let { pathname: n, search: r = "", hash: l = "" } = typeof e == "string" ? _n(e) : e; return { pathname: n ? n.startsWith("/") ? n : Pp(n, t) : t, search: _p(r), hash: bp(l) } } function Pp(e, t) { let n = t.replace(/\/+$/, "").split("/"); return e.split("/").forEach(l => { l === ".." ? n.length > 1 && n.pop() : l !== "." && n.push(l) }), n.length > 1 ? n.join("/") : "/" } function Mi(e, t, n, r) { return "Cannot include a '" + e + "' character in a manually specified " + ("`to." + t + "` field [" + JSON.stringify(r) + "].  Please separate it out to the ") + ("`to." + n + "` field. Alternatively you may provide the full path as ") + 'a string in <Link to="..."> and the router will parse it for you.' } function Cp(e) { return e.filter((t, n) => n === 0 || t.route.path && t.route.path.length > 0) } function ja(e, t) { let n = Cp(e); return t ? n.map((r, l) => l === n.length - 1 ? r.pathname : r.pathnameBase) : n.map(r => r.pathnameBase) } function Sa(e, t, n, r) { r === void 0 && (r = !1); let l; typeof e == "string" ? l = _n(e) : (l = wr({}, e), V(!l.pathname || !l.pathname.includes("?"), Mi("?", "pathname", "search", l)), V(!l.pathname || !l.pathname.includes("#"), Mi("#", "pathname", "hash", l)), V(!l.search || !l.search.includes("#"), Mi("#", "search", "hash", l))); let i = e === "" || l.pathname === "", a = i ? "/" : l.pathname, u; if (a == null) u = n; else { let m = t.length - 1; if (!r && a.startsWith("..")) { let h = a.split("/"); for (; h[0] === "..";)h.shift(), m -= 1; l.pathname = h.join("/") } u = m >= 0 ? t[m] : "/" } let o = kp(l, u), c = a && a !== "/" && a.endsWith("/"), g = (i || a === ".") && n.endsWith("/"); return !o.pathname.endsWith("/") && (c || g) && (o.pathname += "/"), o } const jt = e => e.join("/").replace(/\/\/+/g, "/"), Ep = e => e.replace(/\/+$/, "").replace(/^\/*/, "/"), _p = e => !e || e === "?" ? "" : e.startsWith("?") ? e : "?" + e, bp = e => !e || e === "#" ? "" : e.startsWith("#") ? e : "#" + e; function Mp(e) { return e != null && typeof e.status == "number" && typeof e.statusText == "string" && typeof e.internal == "boolean" && "data" in e } const dd = ["post", "put", "patch", "delete"]; new Set(dd); const Tp = ["get", ...dd]; new Set(Tp);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Nr() { return Nr = Object.assign ? Object.assign.bind() : function (e) { for (var t = 1; t < arguments.length; t++) { var n = arguments[t]; for (var r in n) Object.prototype.hasOwnProperty.call(n, r) && (e[r] = n[r]) } return e }, Nr.apply(this, arguments) } const Kl = N.createContext(null), fd = N.createContext(null), lt = N.createContext(null), Xl = N.createContext(null), _t = N.createContext({ outlet: null, matches: [], isDataRoute: !1 }), md = N.createContext(null); function Dp(e, t) { let { relative: n } = t === void 0 ? {} : t; bn() || V(!1); let { basename: r, navigator: l } = N.useContext(lt), { hash: i, pathname: a, search: u } = Jl(e, { relative: n }), o = a; return r !== "/" && (o = a === "/" ? r : jt([r, a])), l.createHref({ pathname: o, search: u, hash: i }) } function bn() { return N.useContext(Xl) != null } function Mn() { return bn() || V(!1), N.useContext(Xl).location } function pd(e) { N.useContext(lt).static || N.useLayoutEffect(e) } function hd() { let { isDataRoute: e } = N.useContext(_t); return e ? Vp() : Rp() } function Rp() { bn() || V(!1); let e = N.useContext(Kl), { basename: t, future: n, navigator: r } = N.useContext(lt), { matches: l } = N.useContext(_t), { pathname: i } = Mn(), a = JSON.stringify(ja(l, n.v7_relativeSplatPath)), u = N.useRef(!1); return pd(() => { u.current = !0 }), N.useCallback(function (c, g) { if (g === void 0 && (g = {}), !u.current) return; if (typeof c == "number") { r.go(c); return } let m = Sa(c, JSON.parse(a), i, g.relative === "path"); e == null && t !== "/" && (m.pathname = m.pathname === "/" ? t : jt([t, m.pathname])), (g.replace ? r.replace : r.push)(m, g.state, g) }, [t, r, a, i, e]) } function Jl(e, t) { let { relative: n } = t === void 0 ? {} : t, { future: r } = N.useContext(lt), { matches: l } = N.useContext(_t), { pathname: i } = Mn(), a = JSON.stringify(ja(l, r.v7_relativeSplatPath)); return N.useMemo(() => Sa(e, JSON.parse(a), i, n === "path"), [e, a, i, n]) } function Op(e, t) { return Lp(e, t) } function Lp(e, t, n, r) { bn() || V(!1); let { navigator: l } = N.useContext(lt), { matches: i } = N.useContext(_t), a = i[i.length - 1], u = a ? a.params : {}; a && a.pathname; let o = a ? a.pathnameBase : "/"; a && a.route; let c = Mn(), g; if (t) { var m; let k = typeof t == "string" ? _n(t) : t; o === "/" || (m = k.pathname) != null && m.startsWith(o) || V(!1), g = k } else g = c; let h = g.pathname || "/", w = h; if (o !== "/") { let k = o.replace(/^\//, "").split("/"); w = "/" + h.replace(/^\//, "").split("/").slice(k.length).join("/") } let y = cp(e, { pathname: w }), x = Up(y && y.map(k => Object.assign({}, k, { params: Object.assign({}, u, k.params), pathname: jt([o, l.encodeLocation ? l.encodeLocation(k.pathname).pathname : k.pathname]), pathnameBase: k.pathnameBase === "/" ? o : jt([o, l.encodeLocation ? l.encodeLocation(k.pathnameBase).pathname : k.pathnameBase]) })), i, n, r); return t && x ? N.createElement(Xl.Provider, { value: { location: Nr({ pathname: "/", search: "", hash: "", state: null, key: "default" }, g), navigationType: mt.Pop } }, x) : x } function Ip() { let e = Hp(), t = Mp(e) ? e.status + " " + e.statusText : e instanceof Error ? e.message : JSON.stringify(e), n = e instanceof Error ? e.stack : null, l = { padding: "0.5rem", backgroundColor: "rgba(200,200,200, 0.5)" }; return N.createElement(N.Fragment, null, N.createElement("h2", null, "Unexpected Application Error!"), N.createElement("h3", { style: { fontStyle: "italic" } }, t), n ? N.createElement("pre", { style: l }, n) : null, null) } const zp = N.createElement(Ip, null); class Fp extends N.Component { constructor(t) { super(t), this.state = { location: t.location, revalidation: t.revalidation, error: t.error } } static getDerivedStateFromError(t) { return { error: t } } static getDerivedStateFromProps(t, n) { return n.location !== t.location || n.revalidation !== "idle" && t.revalidation === "idle" ? { error: t.error, location: t.location, revalidation: t.revalidation } : { error: t.error !== void 0 ? t.error : n.error, location: n.location, revalidation: t.revalidation || n.revalidation } } componentDidCatch(t, n) { console.error("React Router caught the following error during render", t, n) } render() { return this.state.error !== void 0 ? N.createElement(_t.Provider, { value: this.props.routeContext }, N.createElement(md.Provider, { value: this.state.error, children: this.props.component })) : this.props.children } } function $p(e) { let { routeContext: t, match: n, children: r } = e, l = N.useContext(Kl); return l && l.static && l.staticContext && (n.route.errorElement || n.route.ErrorBoundary) && (l.staticContext._deepestRenderedBoundaryId = n.route.id), N.createElement(_t.Provider, { value: t }, r) } function Up(e, t, n, r) { var l; if (t === void 0 && (t = []), n === void 0 && (n = null), r === void 0 && (r = null), e == null) { var i; if (!n) return null; if (n.errors) e = n.matches; else if ((i = r) != null && i.v7_partialHydration && t.length === 0 && !n.initialized && n.matches.length > 0) e = n.matches; else return null } let a = e, u = (l = n) == null ? void 0 : l.errors; if (u != null) { let g = a.findIndex(m => m.route.id && (u == null ? void 0 : u[m.route.id]) !== void 0); g >= 0 || V(!1), a = a.slice(0, Math.min(a.length, g + 1)) } let o = !1, c = -1; if (n && r && r.v7_partialHydration) for (let g = 0; g < a.length; g++) { let m = a[g]; if ((m.route.HydrateFallback || m.route.hydrateFallbackElement) && (c = g), m.route.id) { let { loaderData: h, errors: w } = n, y = m.route.loader && h[m.route.id] === void 0 && (!w || w[m.route.id] === void 0); if (m.route.lazy || y) { o = !0, c >= 0 ? a = a.slice(0, c + 1) : a = [a[0]]; break } } } return a.reduceRight((g, m, h) => { let w, y = !1, x = null, k = null; n && (w = u && m.route.id ? u[m.route.id] : void 0, x = m.route.errorElement || zp, o && (c < 0 && h === 0 ? (y = !0, k = null) : c === h && (y = !0, k = m.route.hydrateFallbackElement || null))); let f = t.concat(a.slice(0, h + 1)), d = () => { let p; return w ? p = x : y ? p = k : m.route.Component ? p = N.createElement(m.route.Component, null) : m.route.element ? p = m.route.element : p = g, N.createElement($p, { match: m, routeContext: { outlet: g, matches: f, isDataRoute: n != null }, children: p }) }; return n && (m.route.ErrorBoundary || m.route.errorElement || h === 0) ? N.createElement(Fp, { location: n.location, revalidation: n.revalidation, component: x, error: w, children: d(), routeContext: { outlet: null, matches: f, isDataRoute: !0 } }) : d() }, null) } var gd = function (e) { return e.UseBlocker = "useBlocker", e.UseRevalidator = "useRevalidator", e.UseNavigateStable = "useNavigate", e }(gd || {}), Tl = function (e) { return e.UseBlocker = "useBlocker", e.UseLoaderData = "useLoaderData", e.UseActionData = "useActionData", e.UseRouteError = "useRouteError", e.UseNavigation = "useNavigation", e.UseRouteLoaderData = "useRouteLoaderData", e.UseMatches = "useMatches", e.UseRevalidator = "useRevalidator", e.UseNavigateStable = "useNavigate", e.UseRouteId = "useRouteId", e }(Tl || {}); function Ap(e) { let t = N.useContext(Kl); return t || V(!1), t } function Wp(e) { let t = N.useContext(fd); return t || V(!1), t } function Bp(e) { let t = N.useContext(_t); return t || V(!1), t } function yd(e) { let t = Bp(), n = t.matches[t.matches.length - 1]; return n.route.id || V(!1), n.route.id } function Hp() { var e; let t = N.useContext(md), n = Wp(Tl.UseRouteError), r = yd(Tl.UseRouteError); return t !== void 0 ? t : (e = n.errors) == null ? void 0 : e[r] } function Vp() { let { router: e } = Ap(gd.UseNavigateStable), t = yd(Tl.UseNavigateStable), n = N.useRef(!1); return pd(() => { n.current = !0 }), N.useCallback(function (l, i) { i === void 0 && (i = {}), n.current && (typeof l == "number" ? e.navigate(l) : e.navigate(l, Nr({ fromRouteId: t }, i))) }, [e, t]) } function Qp(e, t) { e == null || e.v7_startTransition, e == null || e.v7_relativeSplatPath } function Yp(e) { let { to: t, replace: n, state: r, relative: l } = e; bn() || V(!1); let { future: i, static: a } = N.useContext(lt), { matches: u } = N.useContext(_t), { pathname: o } = Mn(), c = hd(), g = Sa(t, ja(u, i.v7_relativeSplatPath), o, l === "path"), m = JSON.stringify(g); return N.useEffect(() => c(JSON.parse(m), { replace: n, state: r, relative: l }), [c, m, l, n, r]), null } function Ae(e) { V(!1) } function qp(e) { let { basename: t = "/", children: n = null, location: r, navigationType: l = mt.Pop, navigator: i, static: a = !1, future: u } = e; bn() && V(!1); let o = t.replace(/^\/*/, "/"), c = N.useMemo(() => ({ basename: o, navigator: i, static: a, future: Nr({ v7_relativeSplatPath: !1 }, u) }), [o, u, i, a]); typeof r == "string" && (r = _n(r)); let { pathname: g = "/", search: m = "", hash: h = "", state: w = null, key: y = "default" } = r, x = N.useMemo(() => { let k = Sn(g, o); return k == null ? null : { location: { pathname: k, search: m, hash: h, state: w, key: y }, navigationType: l } }, [o, g, m, h, w, y, l]); return x == null ? null : N.createElement(lt.Provider, { value: c }, N.createElement(Xl.Provider, { children: n, value: x })) } function Gp(e) { let { children: t, location: n } = e; return Op(Ps(t), n) } new Promise(() => { }); function Ps(e, t) { t === void 0 && (t = []); let n = []; return N.Children.forEach(e, (r, l) => { if (!N.isValidElement(r)) return; let i = [...t, l]; if (r.type === N.Fragment) { n.push.apply(n, Ps(r.props.children, i)); return } r.type !== Ae && V(!1), !r.props.index || !r.props.children || V(!1); let a = { id: r.props.id || i.join("-"), caseSensitive: r.props.caseSensitive, element: r.props.element, Component: r.props.Component, index: r.props.index, path: r.props.path, loader: r.props.loader, action: r.props.action, errorElement: r.props.errorElement, ErrorBoundary: r.props.ErrorBoundary, hasErrorBoundary: r.props.ErrorBoundary != null || r.props.errorElement != null, shouldRevalidate: r.props.shouldRevalidate, handle: r.props.handle, lazy: r.props.lazy }; r.props.children && (a.children = Ps(r.props.children, i)), n.push(a) }), n }/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Dl() { return Dl = Object.assign ? Object.assign.bind() : function (e) { for (var t = 1; t < arguments.length; t++) { var n = arguments[t]; for (var r in n) Object.prototype.hasOwnProperty.call(n, r) && (e[r] = n[r]) } return e }, Dl.apply(this, arguments) } function vd(e, t) { if (e == null) return {}; var n = {}, r = Object.keys(e), l, i; for (i = 0; i < r.length; i++)l = r[i], !(t.indexOf(l) >= 0) && (n[l] = e[l]); return n } function Kp(e) { return !!(e.metaKey || e.altKey || e.ctrlKey || e.shiftKey) } function Xp(e, t) { return e.button === 0 && (!t || t === "_self") && !Kp(e) } const Jp = ["onClick", "relative", "reloadDocument", "replace", "state", "target", "to", "preventScrollReset", "viewTransition"], Zp = ["aria-current", "caseSensitive", "className", "end", "style", "to", "viewTransition", "children"], eh = "6"; try { window.__reactRouterVersion = eh } catch { } const th = N.createContext({ isTransitioning: !1 }), nh = "startTransition", Uo = qd[nh]; function rh(e) { let { basename: t, children: n, future: r, window: l } = e, i = N.useRef(); i.current == null && (i.current = ap({ window: l, v5Compat: !0 })); let a = i.current, [u, o] = N.useState({ action: a.action, location: a.location }), { v7_startTransition: c } = r || {}, g = N.useCallback(m => { c && Uo ? Uo(() => o(m)) : o(m) }, [o, c]); return N.useLayoutEffect(() => a.listen(g), [a, g]), N.useEffect(() => Qp(r), [r]), N.createElement(qp, { basename: t, children: n, location: u.location, navigationType: u.action, navigator: a, future: r }) } const lh = typeof window < "u" && typeof window.document < "u" && typeof window.document.createElement < "u", ih = /^(?:[a-z][a-z0-9+.-]*:|\/\/)/i, sh = N.forwardRef(function (t, n) { let { onClick: r, relative: l, reloadDocument: i, replace: a, state: u, target: o, to: c, preventScrollReset: g, viewTransition: m } = t, h = vd(t, Jp), { basename: w } = N.useContext(lt), y, x = !1; if (typeof c == "string" && ih.test(c) && (y = c, lh)) try { let p = new URL(window.location.href), v = c.startsWith("//") ? new URL(p.protocol + c) : new URL(c), j = Sn(v.pathname, w); v.origin === p.origin && j != null ? c = j + v.search + v.hash : x = !0 } catch { } let k = Dp(c, { relative: l }), f = uh(c, { replace: a, state: u, target: o, preventScrollReset: g, relative: l, viewTransition: m }); function d(p) { r && r(p), p.defaultPrevented || f(p) } return N.createElement("a", Dl({}, h, { href: y || k, onClick: x || i ? r : d, ref: n, target: o })) }), ah = N.forwardRef(function (t, n) { let { "aria-current": r = "page", caseSensitive: l = !1, className: i = "", end: a = !1, style: u, to: o, viewTransition: c, children: g } = t, m = vd(t, Zp), h = Jl(o, { relative: m.relative }), w = Mn(), y = N.useContext(fd), { navigator: x, basename: k } = N.useContext(lt), f = y != null && ch(h) && c === !0, d = x.encodeLocation ? x.encodeLocation(h).pathname : h.pathname, p = w.pathname, v = y && y.navigation && y.navigation.location ? y.navigation.location.pathname : null; l || (p = p.toLowerCase(), v = v ? v.toLowerCase() : null, d = d.toLowerCase()), v && k && (v = Sn(v, k) || v); const j = d !== "/" && d.endsWith("/") ? d.length - 1 : d.length; let P = p === d || !a && p.startsWith(d) && p.charAt(j) === "/", C = v != null && (v === d || !a && v.startsWith(d) && v.charAt(d.length) === "/"), b = { isActive: P, isPending: C, isTransitioning: f }, W = P ? r : void 0, T; typeof i == "function" ? T = i(b) : T = [i, P ? "active" : null, C ? "pending" : null, f ? "transitioning" : null].filter(Boolean).join(" "); let xe = typeof u == "function" ? u(b) : u; return N.createElement(sh, Dl({}, m, { "aria-current": W, className: T, ref: n, style: xe, to: o, viewTransition: c }), typeof g == "function" ? g(b) : g) }); var Cs; (function (e) { e.UseScrollRestoration = "useScrollRestoration", e.UseSubmit = "useSubmit", e.UseSubmitFetcher = "useSubmitFetcher", e.UseFetcher = "useFetcher", e.useViewTransitionState = "useViewTransitionState" })(Cs || (Cs = {})); var Ao; (function (e) { e.UseFetcher = "useFetcher", e.UseFetchers = "useFetchers", e.UseScrollRestoration = "useScrollRestoration" })(Ao || (Ao = {})); function oh(e) { let t = N.useContext(Kl); return t || V(!1), t } function uh(e, t) { let { target: n, replace: r, state: l, preventScrollReset: i, relative: a, viewTransition: u } = t === void 0 ? {} : t, o = hd(), c = Mn(), g = Jl(e, { relative: a }); return N.useCallback(m => { if (Xp(m, n)) { m.preventDefault(); let h = r !== void 0 ? r : bl(c) === bl(g); o(e, { replace: h, state: l, preventScrollReset: i, relative: a, viewTransition: u }) } }, [c, o, g, r, l, n, e, i, a, u]) } function ch(e, t) { t === void 0 && (t = {}); let n = N.useContext(th); n == null && V(!1); let { basename: r } = oh(Cs.useViewTransitionState), l = Jl(e, { relative: t.relative }); if (!n.isTransitioning) return !1; let i = Sn(n.currentLocation.pathname, r) || n.currentLocation.pathname, a = Sn(n.nextLocation.pathname, r) || n.nextLocation.pathname; return Ml(l.pathname, a) != null || Ml(l.pathname, i) != null } const dh = { patients: [], prescriptions: [], visits: [], users: [{ id: "1", username: "admin", role: "admin", firstName: "System", lastName: "Administrator" }, { id: "2", username: "nurse1", role: "nurse", firstName: "Sarah", lastName: "Johnson", department: "Nursing" }, { id: "3", username: "doctor1", role: "doctor", firstName: "Dr. Michael", lastName: "Smith", department: "General Medicine" }, { id: "4", username: "pharmacist1", role: "pharmacist", firstName: "David", lastName: "Brown", department: "Pharmacy" }, { id: "5", username: "billing1", role: "billing", firstName: "Lisa", lastName: "Wilson", department: "Billing" }], auditLogs: [], currentUser: null }, fh = (e, t) => { switch (t.type) { case "SET_CURRENT_USER": return { ...e, currentUser: t.payload }; case "ADD_PATIENT": return { ...e, patients: [...e.patients, t.payload] }; case "UPDATE_PATIENT": return { ...e, patients: e.patients.map(n => n.id === t.payload.id ? t.payload : n) }; case "ADD_PRESCRIPTION": return { ...e, prescriptions: [...e.prescriptions, t.payload] }; case "UPDATE_PRESCRIPTION": return { ...e, prescriptions: e.prescriptions.map(n => n.id === t.payload.id ? t.payload : n) }; case "ADD_VISIT": return { ...e, visits: [...e.visits, t.payload] }; case "UPDATE_VISIT": return { ...e, visits: e.visits.map(n => n.id === t.payload.id ? t.payload : n) }; case "ADD_AUDIT_LOG": return { ...e, auditLogs: [...e.auditLogs, t.payload] }; default: return e } }, xd = N.createContext(null), mh = ({ children: e }) => { const [t, n] = N.useReducer(fh, dh); return s.jsx(xd.Provider, { value: { state: t, dispatch: n }, children: e }) }, Ye = () => { const e = N.useContext(xd); if (!e) throw new Error("useHospital must be used within a HospitalProvider"); return e };/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var ph = { xmlns: "http://www.w3.org/2000/svg", width: 24, height: 24, viewBox: "0 0 24 24", fill: "none", stroke: "currentColor", strokeWidth: 2, strokeLinecap: "round", strokeLinejoin: "round" };/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hh = e => e.replace(/([a-z0-9])([A-Z])/g, "$1-$2").toLowerCase().trim(), U = (e, t) => { const n = N.forwardRef(({ color: r = "currentColor", size: l = 24, strokeWidth: i = 2, absoluteStrokeWidth: a, className: u = "", children: o, ...c }, g) => N.createElement("svg", { ref: g, ...ph, width: l, height: l, stroke: r, strokeWidth: a ? Number(i) * 24 / Number(l) : i, className: ["lucide", `lucide-${hh(e)}`, u].join(" "), ...c }, [...t.map(([m, h]) => N.createElement(m, h)), ...Array.isArray(o) ? o : [o]])); return n.displayName = `${e}`, n };/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wd = U("Activity", [["path", { d: "M22 12h-4l-3 9L9 3l-3 9H2", key: "d5dnw9" }]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gh = U("AlertCircle", [["circle", { cx: "12", cy: "12", r: "10", key: "1mglay" }], ["line", { x1: "12", x2: "12", y1: "8", y2: "12", key: "1pkeuh" }], ["line", { x1: "12", x2: "12.01", y1: "16", y2: "16", key: "4dfq90" }]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yh = U("AlertTriangle", [["path", { d: "m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z", key: "c3ski4" }], ["path", { d: "M12 9v4", key: "juzpu7" }], ["path", { d: "M12 17h.01", key: "p32p05" }]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vh = U("BarChart3", [["path", { d: "M3 3v18h18", key: "1s2lah" }], ["path", { d: "M18 17V9", key: "2bz60n" }], ["path", { d: "M13 17V5", key: "1frdt8" }], ["path", { d: "M8 17v-3", key: "17ska0" }]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wo = U("CheckCircle", [["path", { d: "M22 11.08V12a10 10 0 1 1-5.93-9.14", key: "g774vq" }], ["path", { d: "m9 11 3 3L22 4", key: "1pflzl" }]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nd = U("Clock", [["circle", { cx: "12", cy: "12", r: "10", key: "1mglay" }], ["polyline", { points: "12 6 12 12 16 14", key: "68esgv" }]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ot = U("CreditCard", [["rect", { width: "20", height: "14", x: "2", y: "5", rx: "2", key: "ynyp8z" }], ["line", { x1: "2", x2: "22", y1: "10", y2: "10", key: "1b3vmo" }]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xh = U("DollarSign", [["line", { x1: "12", x2: "12", y1: "2", y2: "22", key: "7eqyqh" }], ["path", { d: "M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6", key: "1b0p4s" }]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wh = U("Eye", [["path", { d: "M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z", key: "rwhkz3" }], ["circle", { cx: "12", cy: "12", r: "3", key: "1v7zrd" }]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ka = U("FileText", [["path", { d: "M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z", key: "1rqfz7" }], ["path", { d: "M14 2v4a2 2 0 0 0 2 2h4", key: "tnqrlb" }], ["path", { d: "M10 9H8", key: "b1mrlr" }], ["path", { d: "M16 13H8", key: "t4e002" }], ["path", { d: "M16 17H8", key: "z1uh3a" }]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jd = U("Guitar", [["path", { d: "m20 7 1.7-1.7a1 1 0 0 0 0-1.4l-1.6-1.6a1 1 0 0 0-1.4 0L17 4v3Z", key: "15ixgv" }], ["path", { d: "m17 7-5.1 5.1", key: "l9guh7" }], ["circle", { cx: "11.5", cy: "12.5", r: ".5", fill: "currentColor", key: "16onso" }], ["path", { d: "M6 12a2 2 0 0 0 1.8-1.2l.4-.9C8.7 8.8 9.8 8 11 8c2.8 0 5 2.2 5 5 0 1.2-.8 2.3-1.9 2.8l-.9.4A2 2 0 0 0 12 18a4 4 0 0 1-4 4c-3.3 0-6-2.7-6-6a4 4 0 0 1 4-4", key: "x9fguj" }], ["path", { d: "m6 16 2 2", key: "16qmzd" }]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nh = U("LogOut", [["path", { d: "M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4", key: "1uf3rs" }], ["polyline", { points: "16 17 21 12 16 7", key: "1gabdz" }], ["line", { x1: "21", x2: "9", y1: "12", y2: "12", key: "1uyos4" }]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jh = U("Package", [["path", { d: "m7.5 4.27 9 5.15", key: "1c824w" }], ["path", { d: "M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z", key: "hh9hay" }], ["path", { d: "m3.3 7 8.7 5 8.7-5", key: "g66t2b" }], ["path", { d: "M12 22V12", key: "d0xqtd" }]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ut = U("Pill", [["path", { d: "m10.5 20.5 10-10a4.95 4.95 0 1 0-7-7l-10 10a4.95 4.95 0 1 0 7 7Z", key: "wa1lgi" }], ["path", { d: "m8.5 8.5 7 7", key: "rvfmvr" }]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sh = U("Receipt", [["path", { d: "M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1Z", key: "q3az6g" }], ["path", { d: "M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8", key: "1h4pet" }], ["path", { d: "M12 17.5v-11", key: "1jc1ny" }]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kh = U("Save", [["path", { d: "M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z", key: "1owoqh" }], ["polyline", { points: "17 21 17 13 7 13 7 21", key: "1md35c" }], ["polyline", { points: "7 3 7 8 15 8", key: "8nz8an" }]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zl = U("Search", [["circle", { cx: "11", cy: "11", r: "8", key: "4ej97u" }], ["path", { d: "m21 21-4.3-4.3", key: "1qie3q" }]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ph = U("Settings", [["path", { d: "M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z", key: "1qme2f" }], ["circle", { cx: "12", cy: "12", r: "3", key: "1v7zrd" }]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ch = U("SquarePen", [["path", { d: "M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7", key: "1m0v6g" }], ["path", { d: "M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z", key: "1lpok0" }]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nr = U("Stethoscope", [["path", { d: "M4.8 2.3A.3.3 0 1 0 5 2H4a2 2 0 0 0-2 2v5a6 6 0 0 0 6 6v0a6 6 0 0 0 6-6V4a2 2 0 0 0-2-2h-1a.2.2 0 1 0 .3.3", key: "1jd90r" }], ["path", { d: "M8 15v1a6 6 0 0 0 6 6v0a6 6 0 0 0 6-6v-4", key: "126ukv" }], ["circle", { cx: "20", cy: "10", r: "2", key: "ts1r5v" }]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Eh = U("TrendingUp", [["polyline", { points: "22 7 13.5 15.5 8.5 10.5 2 17", key: "126l90" }], ["polyline", { points: "16 7 22 7 22 13", key: "kwv8wd" }]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sd = U("UserCheck", [["path", { d: "M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2", key: "1yyitq" }], ["circle", { cx: "9", cy: "7", r: "4", key: "nufk8" }], ["polyline", { points: "16 11 18 13 22 9", key: "1pwet4" }]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Es = U("UserPlus", [["path", { d: "M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2", key: "1yyitq" }], ["circle", { cx: "9", cy: "7", r: "4", key: "nufk8" }], ["line", { x1: "19", x2: "19", y1: "8", y2: "14", key: "1bvyxn" }], ["line", { x1: "22", x2: "16", y1: "11", y2: "11", key: "1shjgl" }]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kd = U("User", [["path", { d: "M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2", key: "975kel" }], ["circle", { cx: "12", cy: "7", r: "4", key: "17ys0d" }]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const He = U("Users", [["path", { d: "M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2", key: "1yyitq" }], ["circle", { cx: "9", cy: "7", r: "4", key: "nufk8" }], ["path", { d: "M22 21v-2a4 4 0 0 0-3-3.87", key: "kshegd" }], ["path", { d: "M16 3.13a4 4 0 0 1 0 7.75", key: "1da9ce" }]]), _h = ({ onLogin: e }) => { const { state: t } = Ye(); N.useState(""); const [n, r] = N.useState("admin"), l = a => { a.preventDefault(); const u = t.users.find(o => o.role === n); u && e(u) }, i = [{ role: "admin", label: "System Administrator", description: "Full access to all system features" }, { role: "nurse", label: "Nurse", description: "Patient registration and nursing station access" }, { role: "doctor", label: "Doctor", description: "Patient records and treatment management" }, { role: "pharmacist", label: "Pharmacist", description: "Prescription management and dispensing" }, { role: "billing", label: "Billing Staff", description: "Patient billing and payment processing" }]; return s.jsx("div", { className: "min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4", children: s.jsxs("div", { className: "max-w-md w-full bg-white rounded-2xl shadow-xl p-8", children: [s.jsxs("div", { className: "text-center mb-8", children: [s.jsx("div", { className: "flex items-center justify-center w-16 h-16 bg-blue-600 rounded-2xl mx-auto mb-4", children: s.jsx(jd, { className: "w-8 h-8 text-white" }) }), s.jsx("h2", { className: "text-2xl font-bold text-gray-900", children: "MedFlow Hospital" }), s.jsx("p", { className: "text-gray-600 mt-2", children: "Sign in to access the system" })] }), s.jsxs("form", { onSubmit: l, className: "space-y-6", children: [s.jsxs("div", { children: [s.jsx("label", { htmlFor: "role", className: "block text-sm font-medium text-gray-700 mb-3", children: "Select Your Role" }), s.jsx("div", { className: "space-y-3", children: i.map(a => s.jsxs("label", { className: `block p-4 border rounded-lg cursor-pointer transition-all ${n === a.role ? "border-blue-500 bg-blue-50 ring-2 ring-blue-200" : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"}`, children: [s.jsx("input", { type: "radio", name: "role", value: a.role, checked: n === a.role, onChange: u => r(u.target.value), className: "sr-only" }), s.jsxs("div", { className: "flex items-start space-x-3", children: [s.jsx("div", { className: `flex items-center justify-center w-10 h-10 rounded-lg ${n === a.role ? "bg-blue-100" : "bg-gray-100"}`, children: s.jsx(kd, { className: `w-5 h-5 ${n === a.role ? "text-blue-600" : "text-gray-600"}` }) }), s.jsxs("div", { className: "flex-1", children: [s.jsx("h3", { className: "text-sm font-medium text-gray-900", children: a.label }), s.jsx("p", { className: "text-xs text-gray-500 mt-1", children: a.description })] })] })] }, a.role)) })] }), s.jsx("button", { type: "submit", className: "w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors", children: "Sign In" })] }), s.jsx("div", { className: "mt-6 text-center", children: s.jsx("p", { className: "text-xs text-gray-500", children: "Demo system - Select any role to access the application" }) })] }) }) }, bh = ({ onLogout: e }) => { const { state: t } = Ye(), { currentUser: n } = t, r = l => { switch (l) { case "admin": return "bg-purple-100 text-purple-800"; case "doctor": return "bg-blue-100 text-blue-800"; case "nurse": return "bg-green-100 text-green-800"; case "pharmacist": return "bg-orange-100 text-orange-800"; case "billing": return "bg-yellow-100 text-yellow-800"; default: return "bg-gray-100 text-gray-800" } }; return s.jsx("header", { className: "bg-white shadow-md border-b border-gray-200", children: s.jsx("div", { className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8", children: s.jsxs("div", { className: "flex justify-between items-center h-16", children: [s.jsxs("div", { className: "flex items-center space-x-3", children: [s.jsx("div", { className: "flex items-center justify-center w-10 h-10 bg-blue-600 rounded-lg", children: s.jsx(jd, { className: "w-6 h-6 text-white" }) }), s.jsxs("div", { children: [s.jsx("h1", { className: "text-xl font-bold text-gray-900", children: "MedFlow Hospital" }), s.jsx("p", { className: "text-sm text-gray-500", children: "Centralized Information System" })] })] }), n && s.jsxs("div", { className: "flex items-center space-x-4", children: [s.jsxs("div", { className: "flex items-center space-x-3", children: [s.jsx("div", { className: "flex items-center justify-center w-8 h-8 bg-gray-300 rounded-full", children: s.jsx(kd, { className: "w-4 h-4 text-gray-600" }) }), s.jsxs("div", { className: "text-right", children: [s.jsxs("p", { className: "text-sm font-medium text-gray-900", children: [n.firstName, " ", n.lastName] }), s.jsx("span", { className: `inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${r(n.role)}`, children: n.role.charAt(0).toUpperCase() + n.role.slice(1) })] })] }), s.jsxs("button", { onClick: e, className: "flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors", children: [s.jsx(Nh, { className: "w-4 h-4" }), s.jsx("span", { children: "Logout" })] })] })] }) }) }) }, Mh = () => { const { state: e } = Ye(), { currentUser: t } = e, r = (() => { const l = [{ path: "/dashboard", label: "Dashboard", icon: vh }]; switch (t == null ? void 0 : t.role) { case "admin": return [...l, { path: "/patients", label: "All Patients", icon: He }, { path: "/register", label: "Register Patient", icon: Es }, { path: "/nursing", label: "Nursing Station", icon: nr }, { path: "/pharmacy", label: "Pharmacy", icon: Ut }, { path: "/billing", label: "Billing", icon: Ot }, { path: "/audit", label: "Audit Logs", icon: wd }, { path: "/settings", label: "Settings", icon: Ph }]; case "nurse": return [...l, { path: "/register", label: "Register Patient", icon: Es }, { path: "/nursing", label: "Nursing Station", icon: nr }, { path: "/patients", label: "Patient Records", icon: He }]; case "doctor": return [...l, { path: "/patients", label: "Patient Records", icon: He }, { path: "/nursing", label: "Nursing Station", icon: nr }]; case "pharmacist": return [...l, { path: "/pharmacy", label: "Pharmacy", icon: Ut }, { path: "/patients", label: "Patient Records", icon: He }]; case "billing": return [...l, { path: "/billing", label: "Billing", icon: Ot }, { path: "/patients", label: "Patient Records", icon: He }]; default: return l } })(); return s.jsx("aside", { className: "bg-white shadow-md w-64 min-h-screen border-r border-gray-200", children: s.jsx("nav", { className: "mt-8 px-4", children: s.jsx("ul", { className: "space-y-2", children: r.map(l => { const i = l.icon; return s.jsx("li", { children: s.jsxs(ah, { to: l.path, className: ({ isActive: a }) => `flex items-center space-x-3 px-4 py-3 text-sm font-medium rounded-lg transition-colors ${a ? "bg-blue-50 text-blue-700 border-r-2 border-blue-700" : "text-gray-700 hover:bg-gray-50 hover:text-gray-900"}`, children: [s.jsx(i, { className: "w-5 h-5" }), s.jsx("span", { children: l.label })] }) }, l.path) }) }) }) }) }, Th = () => { const { state: e } = Ye(), { patients: t, prescriptions: n, visits: r, currentUser: l } = e, i = { totalPatients: t.length, activePatients: t.filter(c => c.status !== "completed").length, pendingPrescriptions: n.filter(c => c.status === "pending").length, completedVisits: r.filter(c => c.status === "completed").length }, a = t.slice(-5).reverse(), u = e.auditLogs.slice(-10).reverse(), o = c => { switch (c) { case "registered": return "bg-blue-100 text-blue-800"; case "in-treatment": return "bg-yellow-100 text-yellow-800"; case "awaiting-pharmacy": return "bg-orange-100 text-orange-800"; case "awaiting-billing": return "bg-purple-100 text-purple-800"; case "completed": return "bg-green-100 text-green-800"; default: return "bg-gray-100 text-gray-800" } }; return s.jsxs("div", { className: "p-6 max-w-7xl mx-auto", children: [s.jsxs("div", { className: "mb-8", children: [s.jsx("h1", { className: "text-3xl font-bold text-gray-900", children: "Dashboard" }), s.jsxs("p", { className: "text-gray-600 mt-2", children: ["Welcome back, ", l == null ? void 0 : l.firstName, ". Here's what's happening at the hospital today."] })] }), s.jsxs("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8", children: [s.jsx("div", { className: "bg-white rounded-xl p-6 shadow-md border border-gray-200", children: s.jsxs("div", { className: "flex items-center", children: [s.jsx("div", { className: "flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg", children: s.jsx(He, { className: "w-6 h-6 text-blue-600" }) }), s.jsxs("div", { className: "ml-4", children: [s.jsx("p", { className: "text-sm font-medium text-gray-600", children: "Total Patients" }), s.jsx("p", { className: "text-2xl font-bold text-gray-900", children: i.totalPatients })] })] }) }), s.jsx("div", { className: "bg-white rounded-xl p-6 shadow-md border border-gray-200", children: s.jsxs("div", { className: "flex items-center", children: [s.jsx("div", { className: "flex items-center justify-center w-12 h-12 bg-yellow-100 rounded-lg", children: s.jsx(Sd, { className: "w-6 h-6 text-yellow-600" }) }), s.jsxs("div", { className: "ml-4", children: [s.jsx("p", { className: "text-sm font-medium text-gray-600", children: "Active Patients" }), s.jsx("p", { className: "text-2xl font-bold text-gray-900", children: i.activePatients })] })] }) }), s.jsx("div", { className: "bg-white rounded-xl p-6 shadow-md border border-gray-200", children: s.jsxs("div", { className: "flex items-center", children: [s.jsx("div", { className: "flex items-center justify-center w-12 h-12 bg-orange-100 rounded-lg", children: s.jsx(Ut, { className: "w-6 h-6 text-orange-600" }) }), s.jsxs("div", { className: "ml-4", children: [s.jsx("p", { className: "text-sm font-medium text-gray-600", children: "Pending Prescriptions" }), s.jsx("p", { className: "text-2xl font-bold text-gray-900", children: i.pendingPrescriptions })] })] }) }), s.jsx("div", { className: "bg-white rounded-xl p-6 shadow-md border border-gray-200", children: s.jsxs("div", { className: "flex items-center", children: [s.jsx("div", { className: "flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg", children: s.jsx(Eh, { className: "w-6 h-6 text-green-600" }) }), s.jsxs("div", { className: "ml-4", children: [s.jsx("p", { className: "text-sm font-medium text-gray-600", children: "Completed Visits" }), s.jsx("p", { className: "text-2xl font-bold text-gray-900", children: i.completedVisits })] })] }) })] }), s.jsxs("div", { className: "grid grid-cols-1 lg:grid-cols-2 gap-8", children: [s.jsxs("div", { className: "bg-white rounded-xl shadow-md border border-gray-200", children: [s.jsx("div", { className: "px-6 py-4 border-b border-gray-200", children: s.jsx("h3", { className: "text-lg font-semibold text-gray-900", children: "Recent Patients" }) }), s.jsx("div", { className: "p-6", children: a.length === 0 ? s.jsx("p", { className: "text-gray-500 text-center py-8", children: "No patients registered yet" }) : s.jsx("div", { className: "space-y-4", children: a.map(c => s.jsxs("div", { className: "flex items-center justify-between p-3 bg-gray-50 rounded-lg", children: [s.jsxs("div", { children: [s.jsxs("p", { className: "font-medium text-gray-900", children: [c.firstName, " ", c.lastName] }), s.jsx("p", { className: "text-sm text-gray-600", children: c.phone })] }), s.jsxs("div", { className: "text-right", children: [s.jsx("span", { className: `inline-flex px-2 py-1 text-xs font-medium rounded-full ${o(c.status)}`, children: c.status.replace("-", " ") }), s.jsx("p", { className: "text-xs text-gray-500 mt-1", children: c.currentDepartment })] })] }, c.id)) }) })] }), s.jsxs("div", { className: "bg-white rounded-xl shadow-md border border-gray-200", children: [s.jsx("div", { className: "px-6 py-4 border-b border-gray-200", children: s.jsx("h3", { className: "text-lg font-semibold text-gray-900", children: "Recent Activity" }) }), s.jsx("div", { className: "p-6", children: u.length === 0 ? s.jsx("p", { className: "text-gray-500 text-center py-8", children: "No recent activity" }) : s.jsx("div", { className: "space-y-4", children: u.map(c => s.jsxs("div", { className: "flex items-start space-x-3", children: [s.jsx("div", { className: "flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full flex-shrink-0", children: s.jsx(wd, { className: "w-4 h-4 text-blue-600" }) }), s.jsxs("div", { className: "flex-1", children: [s.jsx("p", { className: "text-sm text-gray-900", children: c.details }), s.jsxs("p", { className: "text-xs text-gray-500", children: [new Date(c.timestamp).toLocaleString(), " • ", c.userRole] })] })] }, c.id)) }) })] })] }), s.jsxs("div", { className: "mt-8 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200", children: [s.jsx("h3", { className: "text-lg font-semibold text-gray-900 mb-4", children: "Quick Actions" }), s.jsxs("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-4", children: [s.jsxs("button", { className: "flex items-center space-x-3 p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow", children: [s.jsx(He, { className: "w-6 h-6 text-blue-600" }), s.jsx("span", { className: "font-medium text-gray-900", children: "Register New Patient" })] }), s.jsxs("button", { className: "flex items-center space-x-3 p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow", children: [s.jsx(Nd, { className: "w-6 h-6 text-green-600" }), s.jsx("span", { className: "font-medium text-gray-900", children: "View Patient Queue" })] }), s.jsxs("button", { className: "flex items-center space-x-3 p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow", children: [s.jsx(yh, { className: "w-6 h-6 text-orange-600" }), s.jsx("span", { className: "font-medium text-gray-900", children: "Emergency Alerts" })] })] })] })] }) }, kn = () => Date.now().toString(36) + Math.random().toString(36).substr(2), Dh = () => { const { dispatch: e } = Ye(), [t, n] = N.useState(!1), [r, l] = N.useState({ firstName: "", lastName: "", dateOfBirth: "", gender: "male", phone: "", email: "", address: "", emergencyContactName: "", emergencyContactPhone: "", emergencyContactRelationship: "", medicalHistory: "", allergies: "", currentMedications: "", insuranceNumber: "" }), i = u => { const { name: o, value: c } = u.target; l(g => ({ ...g, [o]: c })) }, a = async u => { u.preventDefault(), n(!0); const o = { id: kn(), firstName: r.firstName, lastName: r.lastName, dateOfBirth: r.dateOfBirth, gender: r.gender, phone: r.phone, email: r.email, address: r.address, emergencyContact: { name: r.emergencyContactName, phone: r.emergencyContactPhone, relationship: r.emergencyContactRelationship }, medicalHistory: r.medicalHistory ? r.medicalHistory.split(",").map(c => c.trim()) : [], allergies: r.allergies ? r.allergies.split(",").map(c => c.trim()) : [], currentMedications: r.currentMedications ? r.currentMedications.split(",").map(c => c.trim()) : [], insuranceNumber: r.insuranceNumber, registrationTime: new Date().toISOString(), currentDepartment: "Casualty", status: "registered" }; e({ type: "ADD_PATIENT", payload: o }), e({ type: "ADD_AUDIT_LOG", payload: { id: kn(), userId: "current-user", userRole: "nurse", action: "CREATE", resource: "Patient", resourceId: o.id, timestamp: new Date().toISOString(), details: `Patient ${o.firstName} ${o.lastName} registered` } }), l({ firstName: "", lastName: "", dateOfBirth: "", gender: "male", phone: "", email: "", address: "", emergencyContactName: "", emergencyContactPhone: "", emergencyContactRelationship: "", medicalHistory: "", allergies: "", currentMedications: "", insuranceNumber: "" }), n(!1), alert("Patient registered successfully!") }; return s.jsx("div", { className: "max-w-4xl mx-auto p-6", children: s.jsxs("div", { className: "bg-white rounded-xl shadow-lg", children: [s.jsx("div", { className: "px-6 py-4 border-b border-gray-200", children: s.jsxs("div", { className: "flex items-center space-x-3", children: [s.jsx("div", { className: "flex items-center justify-center w-10 h-10 bg-green-100 rounded-lg", children: s.jsx(Es, { className: "w-5 h-5 text-green-600" }) }), s.jsxs("div", { children: [s.jsx("h2", { className: "text-xl font-bold text-gray-900", children: "Patient Registration" }), s.jsx("p", { className: "text-sm text-gray-600", children: "Enter patient information for hospital admission" })] })] }) }), s.jsxs("form", { onSubmit: a, className: "p-6", children: [s.jsxs("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-6", children: [s.jsxs("div", { className: "space-y-4", children: [s.jsx("h3", { className: "text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2", children: "Personal Information" }), s.jsxs("div", { children: [s.jsxs("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: ["First Name ", s.jsx("span", { className: "text-red-500", children: "*" })] }), s.jsx("input", { type: "text", name: "firstName", value: r.firstName, onChange: i, required: !0, className: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" })] }), s.jsxs("div", { children: [s.jsxs("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: ["Last Name ", s.jsx("span", { className: "text-red-500", children: "*" })] }), s.jsx("input", { type: "text", name: "lastName", value: r.lastName, onChange: i, required: !0, className: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" })] }), s.jsxs("div", { children: [s.jsxs("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: ["Date of Birth ", s.jsx("span", { className: "text-red-500", children: "*" })] }), s.jsx("input", { type: "date", name: "dateOfBirth", value: r.dateOfBirth, onChange: i, required: !0, className: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" })] }), s.jsxs("div", { children: [s.jsxs("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: ["Gender ", s.jsx("span", { className: "text-red-500", children: "*" })] }), s.jsxs("select", { name: "gender", value: r.gender, onChange: i, required: !0, className: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500", children: [s.jsx("option", { value: "male", children: "Male" }), s.jsx("option", { value: "female", children: "Female" }), s.jsx("option", { value: "other", children: "Other" })] })] }), s.jsxs("div", { children: [s.jsxs("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: ["Phone Number ", s.jsx("span", { className: "text-red-500", children: "*" })] }), s.jsx("input", { type: "tel", name: "phone", value: r.phone, onChange: i, required: !0, className: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" })] }), s.jsxs("div", { children: [s.jsx("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: "Email Address" }), s.jsx("input", { type: "email", name: "email", value: r.email, onChange: i, className: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" })] }), s.jsxs("div", { children: [s.jsxs("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: ["Address ", s.jsx("span", { className: "text-red-500", children: "*" })] }), s.jsx("textarea", { name: "address", value: r.address, onChange: i, required: !0, rows: 3, className: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" })] })] }), s.jsxs("div", { className: "space-y-4", children: [s.jsx("h3", { className: "text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2", children: "Emergency Contact" }), s.jsxs("div", { children: [s.jsxs("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: ["Contact Name ", s.jsx("span", { className: "text-red-500", children: "*" })] }), s.jsx("input", { type: "text", name: "emergencyContactName", value: r.emergencyContactName, onChange: i, required: !0, className: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" })] }), s.jsxs("div", { children: [s.jsxs("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: ["Contact Phone ", s.jsx("span", { className: "text-red-500", children: "*" })] }), s.jsx("input", { type: "tel", name: "emergencyContactPhone", value: r.emergencyContactPhone, onChange: i, required: !0, className: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" })] }), s.jsxs("div", { children: [s.jsxs("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: ["Relationship ", s.jsx("span", { className: "text-red-500", children: "*" })] }), s.jsx("input", { type: "text", name: "emergencyContactRelationship", value: r.emergencyContactRelationship, onChange: i, required: !0, placeholder: "e.g., Spouse, Parent, Sibling", className: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" })] }), s.jsx("h3", { className: "text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2 mt-6", children: "Medical Information" }), s.jsxs("div", { children: [s.jsx("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: "Medical History" }), s.jsx("textarea", { name: "medicalHistory", value: r.medicalHistory, onChange: i, placeholder: "Separate multiple conditions with commas", rows: 3, className: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" })] }), s.jsxs("div", { children: [s.jsx("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: "Allergies" }), s.jsx("textarea", { name: "allergies", value: r.allergies, onChange: i, placeholder: "Separate multiple allergies with commas", rows: 2, className: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" })] }), s.jsxs("div", { children: [s.jsx("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: "Current Medications" }), s.jsx("textarea", { name: "currentMedications", value: r.currentMedications, onChange: i, placeholder: "Separate multiple medications with commas", rows: 2, className: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" })] }), s.jsxs("div", { children: [s.jsx("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: "Insurance Number" }), s.jsx("input", { type: "text", name: "insuranceNumber", value: r.insuranceNumber, onChange: i, className: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" })] })] })] }), s.jsx("div", { className: "mt-8 pt-6 border-t border-gray-200", children: s.jsxs("div", { className: "flex items-center justify-between", children: [s.jsxs("div", { className: "flex items-center space-x-2 text-sm text-gray-600", children: [s.jsx(gh, { className: "w-4 h-4" }), s.jsx("span", { children: "Required fields are marked with *" })] }), s.jsxs("button", { type: "submit", disabled: t, className: "flex items-center space-x-2 bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors", children: [s.jsx(kh, { className: "w-4 h-4" }), s.jsx("span", { children: t ? "Registering..." : "Register Patient" })] })] }) })] })] }) }) }; function $e(e) { const t = Object.prototype.toString.call(e); return e instanceof Date || typeof e == "object" && t === "[object Date]" ? new e.constructor(+e) : typeof e == "number" || t === "[object Number]" || typeof e == "string" || t === "[object String]" ? new Date(e) : new Date(NaN) } function Qt(e, t) { return e instanceof Date ? new e.constructor(t) : new Date(t) } const Pd = 6048e5, Rh = 864e5; let Oh = {}; function ei() { return Oh } function jr(e, t) { var u, o, c, g; const n = ei(), r = (t == null ? void 0 : t.weekStartsOn) ?? ((o = (u = t == null ? void 0 : t.locale) == null ? void 0 : u.options) == null ? void 0 : o.weekStartsOn) ?? n.weekStartsOn ?? ((g = (c = n.locale) == null ? void 0 : c.options) == null ? void 0 : g.weekStartsOn) ?? 0, l = $e(e), i = l.getDay(), a = (i < r ? 7 : 0) + i - r; return l.setDate(l.getDate() - a), l.setHours(0, 0, 0, 0), l } function Rl(e) { return jr(e, { weekStartsOn: 1 }) } function Cd(e) { const t = $e(e), n = t.getFullYear(), r = Qt(e, 0); r.setFullYear(n + 1, 0, 4), r.setHours(0, 0, 0, 0); const l = Rl(r), i = Qt(e, 0); i.setFullYear(n, 0, 4), i.setHours(0, 0, 0, 0); const a = Rl(i); return t.getTime() >= l.getTime() ? n + 1 : t.getTime() >= a.getTime() ? n : n - 1 } function Bo(e) { const t = $e(e); return t.setHours(0, 0, 0, 0), t } function Ho(e) { const t = $e(e), n = new Date(Date.UTC(t.getFullYear(), t.getMonth(), t.getDate(), t.getHours(), t.getMinutes(), t.getSeconds(), t.getMilliseconds())); return n.setUTCFullYear(t.getFullYear()), +e - +n } function Lh(e, t) { const n = Bo(e), r = Bo(t), l = +n - Ho(n), i = +r - Ho(r); return Math.round((l - i) / Rh) } function Ih(e) { const t = Cd(e), n = Qt(e, 0); return n.setFullYear(t, 0, 4), n.setHours(0, 0, 0, 0), Rl(n) } function zh(e) { return e instanceof Date || typeof e == "object" && Object.prototype.toString.call(e) === "[object Date]" } function Fh(e) { if (!zh(e) && typeof e != "number") return !1; const t = $e(e); return !isNaN(Number(t)) } function $h(e) { const t = $e(e), n = Qt(e, 0); return n.setFullYear(t.getFullYear(), 0, 1), n.setHours(0, 0, 0, 0), n } const Uh = { lessThanXSeconds: { one: "less than a second", other: "less than {{count}} seconds" }, xSeconds: { one: "1 second", other: "{{count}} seconds" }, halfAMinute: "half a minute", lessThanXMinutes: { one: "less than a minute", other: "less than {{count}} minutes" }, xMinutes: { one: "1 minute", other: "{{count}} minutes" }, aboutXHours: { one: "about 1 hour", other: "about {{count}} hours" }, xHours: { one: "1 hour", other: "{{count}} hours" }, xDays: { one: "1 day", other: "{{count}} days" }, aboutXWeeks: { one: "about 1 week", other: "about {{count}} weeks" }, xWeeks: { one: "1 week", other: "{{count}} weeks" }, aboutXMonths: { one: "about 1 month", other: "about {{count}} months" }, xMonths: { one: "1 month", other: "{{count}} months" }, aboutXYears: { one: "about 1 year", other: "about {{count}} years" }, xYears: { one: "1 year", other: "{{count}} years" }, overXYears: { one: "over 1 year", other: "over {{count}} years" }, almostXYears: { one: "almost 1 year", other: "almost {{count}} years" } }, Ah = (e, t, n) => { let r; const l = Uh[e]; return typeof l == "string" ? r = l : t === 1 ? r = l.one : r = l.other.replace("{{count}}", t.toString()), n != null && n.addSuffix ? n.comparison && n.comparison > 0 ? "in " + r : r + " ago" : r }; function Ti(e) { return (t = {}) => { const n = t.width ? String(t.width) : e.defaultWidth; return e.formats[n] || e.formats[e.defaultWidth] } } const Wh = { full: "EEEE, MMMM do, y", long: "MMMM do, y", medium: "MMM d, y", short: "MM/dd/yyyy" }, Bh = { full: "h:mm:ss a zzzz", long: "h:mm:ss a z", medium: "h:mm:ss a", short: "h:mm a" }, Hh = { full: "{{date}} 'at' {{time}}", long: "{{date}} 'at' {{time}}", medium: "{{date}}, {{time}}", short: "{{date}}, {{time}}" }, Vh = { date: Ti({ formats: Wh, defaultWidth: "full" }), time: Ti({ formats: Bh, defaultWidth: "full" }), dateTime: Ti({ formats: Hh, defaultWidth: "full" }) }, Qh = { lastWeek: "'last' eeee 'at' p", yesterday: "'yesterday at' p", today: "'today at' p", tomorrow: "'tomorrow at' p", nextWeek: "eeee 'at' p", other: "P" }, Yh = (e, t, n, r) => Qh[e]; function An(e) { return (t, n) => { const r = n != null && n.context ? String(n.context) : "standalone"; let l; if (r === "formatting" && e.formattingValues) { const a = e.defaultFormattingWidth || e.defaultWidth, u = n != null && n.width ? String(n.width) : a; l = e.formattingValues[u] || e.formattingValues[a] } else { const a = e.defaultWidth, u = n != null && n.width ? String(n.width) : e.defaultWidth; l = e.values[u] || e.values[a] } const i = e.argumentCallback ? e.argumentCallback(t) : t; return l[i] } } const qh = { narrow: ["B", "A"], abbreviated: ["BC", "AD"], wide: ["Before Christ", "Anno Domini"] }, Gh = { narrow: ["1", "2", "3", "4"], abbreviated: ["Q1", "Q2", "Q3", "Q4"], wide: ["1st quarter", "2nd quarter", "3rd quarter", "4th quarter"] }, Kh = { narrow: ["J", "F", "M", "A", "M", "J", "J", "A", "S", "O", "N", "D"], abbreviated: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"], wide: ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"] }, Xh = { narrow: ["S", "M", "T", "W", "T", "F", "S"], short: ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"], abbreviated: ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"], wide: ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"] }, Jh = { narrow: { am: "a", pm: "p", midnight: "mi", noon: "n", morning: "morning", afternoon: "afternoon", evening: "evening", night: "night" }, abbreviated: { am: "AM", pm: "PM", midnight: "midnight", noon: "noon", morning: "morning", afternoon: "afternoon", evening: "evening", night: "night" }, wide: { am: "a.m.", pm: "p.m.", midnight: "midnight", noon: "noon", morning: "morning", afternoon: "afternoon", evening: "evening", night: "night" } }, Zh = { narrow: { am: "a", pm: "p", midnight: "mi", noon: "n", morning: "in the morning", afternoon: "in the afternoon", evening: "in the evening", night: "at night" }, abbreviated: { am: "AM", pm: "PM", midnight: "midnight", noon: "noon", morning: "in the morning", afternoon: "in the afternoon", evening: "in the evening", night: "at night" }, wide: { am: "a.m.", pm: "p.m.", midnight: "midnight", noon: "noon", morning: "in the morning", afternoon: "in the afternoon", evening: "in the evening", night: "at night" } }, e0 = (e, t) => { const n = Number(e), r = n % 100; if (r > 20 || r < 10) switch (r % 10) { case 1: return n + "st"; case 2: return n + "nd"; case 3: return n + "rd" }return n + "th" }, t0 = { ordinalNumber: e0, era: An({ values: qh, defaultWidth: "wide" }), quarter: An({ values: Gh, defaultWidth: "wide", argumentCallback: e => e - 1 }), month: An({ values: Kh, defaultWidth: "wide" }), day: An({ values: Xh, defaultWidth: "wide" }), dayPeriod: An({ values: Jh, defaultWidth: "wide", formattingValues: Zh, defaultFormattingWidth: "wide" }) }; function Wn(e) { return (t, n = {}) => { const r = n.width, l = r && e.matchPatterns[r] || e.matchPatterns[e.defaultMatchWidth], i = t.match(l); if (!i) return null; const a = i[0], u = r && e.parsePatterns[r] || e.parsePatterns[e.defaultParseWidth], o = Array.isArray(u) ? r0(u, m => m.test(a)) : n0(u, m => m.test(a)); let c; c = e.valueCallback ? e.valueCallback(o) : o, c = n.valueCallback ? n.valueCallback(c) : c; const g = t.slice(a.length); return { value: c, rest: g } } } function n0(e, t) { for (const n in e) if (Object.prototype.hasOwnProperty.call(e, n) && t(e[n])) return n } function r0(e, t) { for (let n = 0; n < e.length; n++)if (t(e[n])) return n } function l0(e) { return (t, n = {}) => { const r = t.match(e.matchPattern); if (!r) return null; const l = r[0], i = t.match(e.parsePattern); if (!i) return null; let a = e.valueCallback ? e.valueCallback(i[0]) : i[0]; a = n.valueCallback ? n.valueCallback(a) : a; const u = t.slice(l.length); return { value: a, rest: u } } } const i0 = /^(\d+)(th|st|nd|rd)?/i, s0 = /\d+/i, a0 = { narrow: /^(b|a)/i, abbreviated: /^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i, wide: /^(before christ|before common era|anno domini|common era)/i }, o0 = { any: [/^b/i, /^(a|c)/i] }, u0 = { narrow: /^[1234]/i, abbreviated: /^q[1234]/i, wide: /^[1234](th|st|nd|rd)? quarter/i }, c0 = { any: [/1/i, /2/i, /3/i, /4/i] }, d0 = { narrow: /^[jfmasond]/i, abbreviated: /^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i, wide: /^(january|february|march|april|may|june|july|august|september|october|november|december)/i }, f0 = { narrow: [/^j/i, /^f/i, /^m/i, /^a/i, /^m/i, /^j/i, /^j/i, /^a/i, /^s/i, /^o/i, /^n/i, /^d/i], any: [/^ja/i, /^f/i, /^mar/i, /^ap/i, /^may/i, /^jun/i, /^jul/i, /^au/i, /^s/i, /^o/i, /^n/i, /^d/i] }, m0 = { narrow: /^[smtwf]/i, short: /^(su|mo|tu|we|th|fr|sa)/i, abbreviated: /^(sun|mon|tue|wed|thu|fri|sat)/i, wide: /^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i }, p0 = { narrow: [/^s/i, /^m/i, /^t/i, /^w/i, /^t/i, /^f/i, /^s/i], any: [/^su/i, /^m/i, /^tu/i, /^w/i, /^th/i, /^f/i, /^sa/i] }, h0 = { narrow: /^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i, any: /^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i }, g0 = { any: { am: /^a/i, pm: /^p/i, midnight: /^mi/i, noon: /^no/i, morning: /morning/i, afternoon: /afternoon/i, evening: /evening/i, night: /night/i } }, y0 = { ordinalNumber: l0({ matchPattern: i0, parsePattern: s0, valueCallback: e => parseInt(e, 10) }), era: Wn({ matchPatterns: a0, defaultMatchWidth: "wide", parsePatterns: o0, defaultParseWidth: "any" }), quarter: Wn({ matchPatterns: u0, defaultMatchWidth: "wide", parsePatterns: c0, defaultParseWidth: "any", valueCallback: e => e + 1 }), month: Wn({ matchPatterns: d0, defaultMatchWidth: "wide", parsePatterns: f0, defaultParseWidth: "any" }), day: Wn({ matchPatterns: m0, defaultMatchWidth: "wide", parsePatterns: p0, defaultParseWidth: "any" }), dayPeriod: Wn({ matchPatterns: h0, defaultMatchWidth: "any", parsePatterns: g0, defaultParseWidth: "any" }) }, v0 = { code: "en-US", formatDistance: Ah, formatLong: Vh, formatRelative: Yh, localize: t0, match: y0, options: { weekStartsOn: 0, firstWeekContainsDate: 1 } }; function x0(e) { const t = $e(e); return Lh(t, $h(t)) + 1 } function w0(e) { const t = $e(e), n = +Rl(t) - +Ih(t); return Math.round(n / Pd) + 1 } function Ed(e, t) { var g, m, h, w; const n = $e(e), r = n.getFullYear(), l = ei(), i = (t == null ? void 0 : t.firstWeekContainsDate) ?? ((m = (g = t == null ? void 0 : t.locale) == null ? void 0 : g.options) == null ? void 0 : m.firstWeekContainsDate) ?? l.firstWeekContainsDate ?? ((w = (h = l.locale) == null ? void 0 : h.options) == null ? void 0 : w.firstWeekContainsDate) ?? 1, a = Qt(e, 0); a.setFullYear(r + 1, 0, i), a.setHours(0, 0, 0, 0); const u = jr(a, t), o = Qt(e, 0); o.setFullYear(r, 0, i), o.setHours(0, 0, 0, 0); const c = jr(o, t); return n.getTime() >= u.getTime() ? r + 1 : n.getTime() >= c.getTime() ? r : r - 1 } function N0(e, t) { var u, o, c, g; const n = ei(), r = (t == null ? void 0 : t.firstWeekContainsDate) ?? ((o = (u = t == null ? void 0 : t.locale) == null ? void 0 : u.options) == null ? void 0 : o.firstWeekContainsDate) ?? n.firstWeekContainsDate ?? ((g = (c = n.locale) == null ? void 0 : c.options) == null ? void 0 : g.firstWeekContainsDate) ?? 1, l = Ed(e, t), i = Qt(e, 0); return i.setFullYear(l, 0, r), i.setHours(0, 0, 0, 0), jr(i, t) } function j0(e, t) { const n = $e(e), r = +jr(n, t) - +N0(n, t); return Math.round(r / Pd) + 1 } function L(e, t) { const n = e < 0 ? "-" : "", r = Math.abs(e).toString().padStart(t, "0"); return n + r } const st = { y(e, t) { const n = e.getFullYear(), r = n > 0 ? n : 1 - n; return L(t === "yy" ? r % 100 : r, t.length) }, M(e, t) { const n = e.getMonth(); return t === "M" ? String(n + 1) : L(n + 1, 2) }, d(e, t) { return L(e.getDate(), t.length) }, a(e, t) { const n = e.getHours() / 12 >= 1 ? "pm" : "am"; switch (t) { case "a": case "aa": return n.toUpperCase(); case "aaa": return n; case "aaaaa": return n[0]; case "aaaa": default: return n === "am" ? "a.m." : "p.m." } }, h(e, t) { return L(e.getHours() % 12 || 12, t.length) }, H(e, t) { return L(e.getHours(), t.length) }, m(e, t) { return L(e.getMinutes(), t.length) }, s(e, t) { return L(e.getSeconds(), t.length) }, S(e, t) { const n = t.length, r = e.getMilliseconds(), l = Math.trunc(r * Math.pow(10, n - 3)); return L(l, t.length) } }, Kt = { am: "am", pm: "pm", midnight: "midnight", noon: "noon", morning: "morning", afternoon: "afternoon", evening: "evening", night: "night" }, Vo = { G: function (e, t, n) { const r = e.getFullYear() > 0 ? 1 : 0; switch (t) { case "G": case "GG": case "GGG": return n.era(r, { width: "abbreviated" }); case "GGGGG": return n.era(r, { width: "narrow" }); case "GGGG": default: return n.era(r, { width: "wide" }) } }, y: function (e, t, n) { if (t === "yo") { const r = e.getFullYear(), l = r > 0 ? r : 1 - r; return n.ordinalNumber(l, { unit: "year" }) } return st.y(e, t) }, Y: function (e, t, n, r) { const l = Ed(e, r), i = l > 0 ? l : 1 - l; if (t === "YY") { const a = i % 100; return L(a, 2) } return t === "Yo" ? n.ordinalNumber(i, { unit: "year" }) : L(i, t.length) }, R: function (e, t) { const n = Cd(e); return L(n, t.length) }, u: function (e, t) { const n = e.getFullYear(); return L(n, t.length) }, Q: function (e, t, n) { const r = Math.ceil((e.getMonth() + 1) / 3); switch (t) { case "Q": return String(r); case "QQ": return L(r, 2); case "Qo": return n.ordinalNumber(r, { unit: "quarter" }); case "QQQ": return n.quarter(r, { width: "abbreviated", context: "formatting" }); case "QQQQQ": return n.quarter(r, { width: "narrow", context: "formatting" }); case "QQQQ": default: return n.quarter(r, { width: "wide", context: "formatting" }) } }, q: function (e, t, n) { const r = Math.ceil((e.getMonth() + 1) / 3); switch (t) { case "q": return String(r); case "qq": return L(r, 2); case "qo": return n.ordinalNumber(r, { unit: "quarter" }); case "qqq": return n.quarter(r, { width: "abbreviated", context: "standalone" }); case "qqqqq": return n.quarter(r, { width: "narrow", context: "standalone" }); case "qqqq": default: return n.quarter(r, { width: "wide", context: "standalone" }) } }, M: function (e, t, n) { const r = e.getMonth(); switch (t) { case "M": case "MM": return st.M(e, t); case "Mo": return n.ordinalNumber(r + 1, { unit: "month" }); case "MMM": return n.month(r, { width: "abbreviated", context: "formatting" }); case "MMMMM": return n.month(r, { width: "narrow", context: "formatting" }); case "MMMM": default: return n.month(r, { width: "wide", context: "formatting" }) } }, L: function (e, t, n) { const r = e.getMonth(); switch (t) { case "L": return String(r + 1); case "LL": return L(r + 1, 2); case "Lo": return n.ordinalNumber(r + 1, { unit: "month" }); case "LLL": return n.month(r, { width: "abbreviated", context: "standalone" }); case "LLLLL": return n.month(r, { width: "narrow", context: "standalone" }); case "LLLL": default: return n.month(r, { width: "wide", context: "standalone" }) } }, w: function (e, t, n, r) { const l = j0(e, r); return t === "wo" ? n.ordinalNumber(l, { unit: "week" }) : L(l, t.length) }, I: function (e, t, n) { const r = w0(e); return t === "Io" ? n.ordinalNumber(r, { unit: "week" }) : L(r, t.length) }, d: function (e, t, n) { return t === "do" ? n.ordinalNumber(e.getDate(), { unit: "date" }) : st.d(e, t) }, D: function (e, t, n) { const r = x0(e); return t === "Do" ? n.ordinalNumber(r, { unit: "dayOfYear" }) : L(r, t.length) }, E: function (e, t, n) { const r = e.getDay(); switch (t) { case "E": case "EE": case "EEE": return n.day(r, { width: "abbreviated", context: "formatting" }); case "EEEEE": return n.day(r, { width: "narrow", context: "formatting" }); case "EEEEEE": return n.day(r, { width: "short", context: "formatting" }); case "EEEE": default: return n.day(r, { width: "wide", context: "formatting" }) } }, e: function (e, t, n, r) { const l = e.getDay(), i = (l - r.weekStartsOn + 8) % 7 || 7; switch (t) { case "e": return String(i); case "ee": return L(i, 2); case "eo": return n.ordinalNumber(i, { unit: "day" }); case "eee": return n.day(l, { width: "abbreviated", context: "formatting" }); case "eeeee": return n.day(l, { width: "narrow", context: "formatting" }); case "eeeeee": return n.day(l, { width: "short", context: "formatting" }); case "eeee": default: return n.day(l, { width: "wide", context: "formatting" }) } }, c: function (e, t, n, r) { const l = e.getDay(), i = (l - r.weekStartsOn + 8) % 7 || 7; switch (t) { case "c": return String(i); case "cc": return L(i, t.length); case "co": return n.ordinalNumber(i, { unit: "day" }); case "ccc": return n.day(l, { width: "abbreviated", context: "standalone" }); case "ccccc": return n.day(l, { width: "narrow", context: "standalone" }); case "cccccc": return n.day(l, { width: "short", context: "standalone" }); case "cccc": default: return n.day(l, { width: "wide", context: "standalone" }) } }, i: function (e, t, n) { const r = e.getDay(), l = r === 0 ? 7 : r; switch (t) { case "i": return String(l); case "ii": return L(l, t.length); case "io": return n.ordinalNumber(l, { unit: "day" }); case "iii": return n.day(r, { width: "abbreviated", context: "formatting" }); case "iiiii": return n.day(r, { width: "narrow", context: "formatting" }); case "iiiiii": return n.day(r, { width: "short", context: "formatting" }); case "iiii": default: return n.day(r, { width: "wide", context: "formatting" }) } }, a: function (e, t, n) { const l = e.getHours() / 12 >= 1 ? "pm" : "am"; switch (t) { case "a": case "aa": return n.dayPeriod(l, { width: "abbreviated", context: "formatting" }); case "aaa": return n.dayPeriod(l, { width: "abbreviated", context: "formatting" }).toLowerCase(); case "aaaaa": return n.dayPeriod(l, { width: "narrow", context: "formatting" }); case "aaaa": default: return n.dayPeriod(l, { width: "wide", context: "formatting" }) } }, b: function (e, t, n) { const r = e.getHours(); let l; switch (r === 12 ? l = Kt.noon : r === 0 ? l = Kt.midnight : l = r / 12 >= 1 ? "pm" : "am", t) { case "b": case "bb": return n.dayPeriod(l, { width: "abbreviated", context: "formatting" }); case "bbb": return n.dayPeriod(l, { width: "abbreviated", context: "formatting" }).toLowerCase(); case "bbbbb": return n.dayPeriod(l, { width: "narrow", context: "formatting" }); case "bbbb": default: return n.dayPeriod(l, { width: "wide", context: "formatting" }) } }, B: function (e, t, n) { const r = e.getHours(); let l; switch (r >= 17 ? l = Kt.evening : r >= 12 ? l = Kt.afternoon : r >= 4 ? l = Kt.morning : l = Kt.night, t) { case "B": case "BB": case "BBB": return n.dayPeriod(l, { width: "abbreviated", context: "formatting" }); case "BBBBB": return n.dayPeriod(l, { width: "narrow", context: "formatting" }); case "BBBB": default: return n.dayPeriod(l, { width: "wide", context: "formatting" }) } }, h: function (e, t, n) { if (t === "ho") { let r = e.getHours() % 12; return r === 0 && (r = 12), n.ordinalNumber(r, { unit: "hour" }) } return st.h(e, t) }, H: function (e, t, n) { return t === "Ho" ? n.ordinalNumber(e.getHours(), { unit: "hour" }) : st.H(e, t) }, K: function (e, t, n) { const r = e.getHours() % 12; return t === "Ko" ? n.ordinalNumber(r, { unit: "hour" }) : L(r, t.length) }, k: function (e, t, n) { let r = e.getHours(); return r === 0 && (r = 24), t === "ko" ? n.ordinalNumber(r, { unit: "hour" }) : L(r, t.length) }, m: function (e, t, n) { return t === "mo" ? n.ordinalNumber(e.getMinutes(), { unit: "minute" }) : st.m(e, t) }, s: function (e, t, n) { return t === "so" ? n.ordinalNumber(e.getSeconds(), { unit: "second" }) : st.s(e, t) }, S: function (e, t) { return st.S(e, t) }, X: function (e, t, n) { const r = e.getTimezoneOffset(); if (r === 0) return "Z"; switch (t) { case "X": return Yo(r); case "XXXX": case "XX": return Rt(r); case "XXXXX": case "XXX": default: return Rt(r, ":") } }, x: function (e, t, n) { const r = e.getTimezoneOffset(); switch (t) { case "x": return Yo(r); case "xxxx": case "xx": return Rt(r); case "xxxxx": case "xxx": default: return Rt(r, ":") } }, O: function (e, t, n) { const r = e.getTimezoneOffset(); switch (t) { case "O": case "OO": case "OOO": return "GMT" + Qo(r, ":"); case "OOOO": default: return "GMT" + Rt(r, ":") } }, z: function (e, t, n) { const r = e.getTimezoneOffset(); switch (t) { case "z": case "zz": case "zzz": return "GMT" + Qo(r, ":"); case "zzzz": default: return "GMT" + Rt(r, ":") } }, t: function (e, t, n) { const r = Math.trunc(e.getTime() / 1e3); return L(r, t.length) }, T: function (e, t, n) { const r = e.getTime(); return L(r, t.length) } }; function Qo(e, t = "") { const n = e > 0 ? "-" : "+", r = Math.abs(e), l = Math.trunc(r / 60), i = r % 60; return i === 0 ? n + String(l) : n + String(l) + t + L(i, 2) } function Yo(e, t) { return e % 60 === 0 ? (e > 0 ? "-" : "+") + L(Math.abs(e) / 60, 2) : Rt(e, t) } function Rt(e, t = "") { const n = e > 0 ? "-" : "+", r = Math.abs(e), l = L(Math.trunc(r / 60), 2), i = L(r % 60, 2); return n + l + t + i } const qo = (e, t) => { switch (e) { case "P": return t.date({ width: "short" }); case "PP": return t.date({ width: "medium" }); case "PPP": return t.date({ width: "long" }); case "PPPP": default: return t.date({ width: "full" }) } }, _d = (e, t) => { switch (e) { case "p": return t.time({ width: "short" }); case "pp": return t.time({ width: "medium" }); case "ppp": return t.time({ width: "long" }); case "pppp": default: return t.time({ width: "full" }) } }, S0 = (e, t) => { const n = e.match(/(P+)(p+)?/) || [], r = n[1], l = n[2]; if (!l) return qo(e, t); let i; switch (r) { case "P": i = t.dateTime({ width: "short" }); break; case "PP": i = t.dateTime({ width: "medium" }); break; case "PPP": i = t.dateTime({ width: "long" }); break; case "PPPP": default: i = t.dateTime({ width: "full" }); break }return i.replace("{{date}}", qo(r, t)).replace("{{time}}", _d(l, t)) }, k0 = { p: _d, P: S0 }, P0 = /^D+$/, C0 = /^Y+$/, E0 = ["D", "DD", "YY", "YYYY"]; function _0(e) { return P0.test(e) } function b0(e) { return C0.test(e) } function M0(e, t, n) { const r = T0(e, t, n); if (console.warn(r), E0.includes(e)) throw new RangeError(r) } function T0(e, t, n) { const r = e[0] === "Y" ? "years" : "days of the month"; return `Use \`${e.toLowerCase()}\` instead of \`${e}\` (in \`${t}\`) for formatting ${r} to the input \`${n}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md` } const D0 = /[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g, R0 = /P+p+|P+|p+|''|'(''|[^'])+('|$)|./g, O0 = /^'([^]*?)'?$/, L0 = /''/g, I0 = /[a-zA-Z]/; function pe(e, t, n) { var g, m, h, w; const r = ei(), l = r.locale ?? v0, i = r.firstWeekContainsDate ?? ((m = (g = r.locale) == null ? void 0 : g.options) == null ? void 0 : m.firstWeekContainsDate) ?? 1, a = r.weekStartsOn ?? ((w = (h = r.locale) == null ? void 0 : h.options) == null ? void 0 : w.weekStartsOn) ?? 0, u = $e(e); if (!Fh(u)) throw new RangeError("Invalid time value"); let o = t.match(R0).map(y => { const x = y[0]; if (x === "p" || x === "P") { const k = k0[x]; return k(y, l.formatLong) } return y }).join("").match(D0).map(y => { if (y === "''") return { isToken: !1, value: "'" }; const x = y[0]; if (x === "'") return { isToken: !1, value: z0(y) }; if (Vo[x]) return { isToken: !0, value: y }; if (x.match(I0)) throw new RangeError("Format string contains an unescaped latin alphabet character `" + x + "`"); return { isToken: !1, value: y } }); l.localize.preprocessor && (o = l.localize.preprocessor(u, o)); const c = { firstWeekContainsDate: i, weekStartsOn: a, locale: l }; return o.map(y => { if (!y.isToken) return y.value; const x = y.value; (b0(x) || _0(x)) && M0(x, t, String(e)); const k = Vo[x[0]]; return k(u, x, l.localize, c) }).join("") } function z0(e) { const t = e.match(O0); return t ? t[1].replace(L0, "'") : e } const F0 = () => { const { state: e } = Ye(), { patients: t } = e, [n, r] = N.useState(""), [l, i] = N.useState(null), a = t.filter(c => `${c.firstName} ${c.lastName}`.toLowerCase().includes(n.toLowerCase()) || c.phone.includes(n) || c.email.toLowerCase().includes(n.toLowerCase())), u = c => { switch (c) { case "registered": return "bg-blue-100 text-blue-800"; case "in-treatment": return "bg-yellow-100 text-yellow-800"; case "awaiting-pharmacy": return "bg-orange-100 text-orange-800"; case "awaiting-billing": return "bg-purple-100 text-purple-800"; case "completed": return "bg-green-100 text-green-800"; default: return "bg-gray-100 text-gray-800" } }, o = l ? t.find(c => c.id === l) : null; return s.jsxs("div", { className: "p-6 max-w-7xl mx-auto", children: [s.jsxs("div", { className: "mb-8", children: [s.jsxs("div", { className: "flex items-center space-x-3 mb-4", children: [s.jsx("div", { className: "flex items-center justify-center w-10 h-10 bg-blue-100 rounded-lg", children: s.jsx(He, { className: "w-5 h-5 text-blue-600" }) }), s.jsxs("div", { children: [s.jsx("h1", { className: "text-2xl font-bold text-gray-900", children: "Patient Records" }), s.jsx("p", { className: "text-gray-600", children: "Manage and view all patient information" })] })] }), s.jsxs("div", { className: "relative", children: [s.jsx(Zl, { className: "absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" }), s.jsx("input", { type: "text", placeholder: "Search patients by name, phone, or email...", value: n, onChange: c => r(c.target.value), className: "w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" })] })] }), s.jsxs("div", { className: "grid grid-cols-1 lg:grid-cols-3 gap-8", children: [s.jsx("div", { className: "lg:col-span-2", children: s.jsxs("div", { className: "bg-white rounded-xl shadow-md border border-gray-200", children: [s.jsx("div", { className: "px-6 py-4 border-b border-gray-200", children: s.jsxs("h3", { className: "text-lg font-semibold text-gray-900", children: ["All Patients (", a.length, ")"] }) }), s.jsx("div", { className: "divide-y divide-gray-200", children: a.length === 0 ? s.jsxs("div", { className: "p-8 text-center", children: [s.jsx(He, { className: "w-12 h-12 text-gray-400 mx-auto mb-4" }), s.jsx("p", { className: "text-gray-500", children: "No patients found" })] }) : a.map(c => s.jsx("div", { className: `p-6 hover:bg-gray-50 cursor-pointer transition-colors ${l === c.id ? "bg-blue-50 border-l-4 border-blue-500" : ""}`, onClick: () => i(c.id), children: s.jsxs("div", { className: "flex items-center justify-between", children: [s.jsxs("div", { className: "flex-1", children: [s.jsxs("div", { className: "flex items-center space-x-3", children: [s.jsxs("h4", { className: "text-lg font-medium text-gray-900", children: [c.firstName, " ", c.lastName] }), s.jsx("span", { className: `inline-flex px-2 py-1 text-xs font-medium rounded-full ${u(c.status)}`, children: c.status.replace("-", " ") })] }), s.jsxs("div", { className: "mt-2 grid grid-cols-2 gap-4 text-sm text-gray-600", children: [s.jsxs("div", { children: [s.jsxs("p", { children: [s.jsx("strong", { children: "DOB:" }), " ", pe(new Date(c.dateOfBirth), "MMM dd, yyyy")] }), s.jsxs("p", { children: [s.jsx("strong", { children: "Phone:" }), " ", c.phone] })] }), s.jsxs("div", { children: [s.jsxs("p", { children: [s.jsx("strong", { children: "Department:" }), " ", c.currentDepartment] }), s.jsxs("p", { children: [s.jsx("strong", { children: "Registered:" }), " ", pe(new Date(c.registrationTime), "MMM dd, HH:mm")] })] })] })] }), s.jsxs("div", { className: "flex items-center space-x-2", children: [s.jsx("button", { className: "p-2 text-gray-400 hover:text-gray-600 transition-colors", children: s.jsx(wh, { className: "w-4 h-4" }) }), s.jsx("button", { className: "p-2 text-gray-400 hover:text-gray-600 transition-colors", children: s.jsx(Ch, { className: "w-4 h-4" }) }), s.jsx("button", { className: "p-2 text-gray-400 hover:text-gray-600 transition-colors", children: s.jsx(ka, { className: "w-4 h-4" }) })] })] }) }, c.id)) })] }) }), s.jsx("div", { className: "lg:col-span-1", children: s.jsxs("div", { className: "bg-white rounded-xl shadow-md border border-gray-200 sticky top-6", children: [s.jsx("div", { className: "px-6 py-4 border-b border-gray-200", children: s.jsx("h3", { className: "text-lg font-semibold text-gray-900", children: "Patient Details" }) }), s.jsx("div", { className: "p-6", children: o ? s.jsxs("div", { className: "space-y-6", children: [s.jsxs("div", { children: [s.jsx("h4", { className: "text-sm font-medium text-gray-900 mb-3", children: "Personal Information" }), s.jsxs("div", { className: "space-y-2 text-sm", children: [s.jsxs("p", { children: [s.jsx("strong", { children: "Name:" }), " ", o.firstName, " ", o.lastName] }), s.jsxs("p", { children: [s.jsx("strong", { children: "DOB:" }), " ", pe(new Date(o.dateOfBirth), "MMM dd, yyyy")] }), s.jsxs("p", { children: [s.jsx("strong", { children: "Gender:" }), " ", o.gender] }), s.jsxs("p", { children: [s.jsx("strong", { children: "Phone:" }), " ", o.phone] }), s.jsxs("p", { children: [s.jsx("strong", { children: "Email:" }), " ", o.email] })] })] }), s.jsxs("div", { children: [s.jsx("h4", { className: "text-sm font-medium text-gray-900 mb-3", children: "Emergency Contact" }), s.jsxs("div", { className: "space-y-2 text-sm", children: [s.jsxs("p", { children: [s.jsx("strong", { children: "Name:" }), " ", o.emergencyContact.name] }), s.jsxs("p", { children: [s.jsx("strong", { children: "Phone:" }), " ", o.emergencyContact.phone] }), s.jsxs("p", { children: [s.jsx("strong", { children: "Relationship:" }), " ", o.emergencyContact.relationship] })] })] }), s.jsxs("div", { children: [s.jsx("h4", { className: "text-sm font-medium text-gray-900 mb-3", children: "Medical Information" }), s.jsxs("div", { className: "space-y-2 text-sm", children: [s.jsxs("div", { children: [s.jsx("p", { className: "font-medium", children: "Allergies:" }), s.jsx("p", { className: "text-gray-600", children: o.allergies.length > 0 ? o.allergies.join(", ") : "None" })] }), s.jsxs("div", { children: [s.jsx("p", { className: "font-medium", children: "Current Medications:" }), s.jsx("p", { className: "text-gray-600", children: o.currentMedications.length > 0 ? o.currentMedications.join(", ") : "None" })] })] })] }), s.jsxs("div", { children: [s.jsx("h4", { className: "text-sm font-medium text-gray-900 mb-3", children: "Current Status" }), s.jsxs("div", { className: "space-y-2", children: [s.jsx("span", { className: `inline-flex px-3 py-1 text-sm font-medium rounded-full ${u(o.status)}`, children: o.status.replace("-", " ") }), s.jsxs("p", { className: "text-sm text-gray-600", children: ["Department: ", o.currentDepartment] })] })] })] }) : s.jsxs("div", { className: "text-center py-8", children: [s.jsx(He, { className: "w-12 h-12 text-gray-400 mx-auto mb-4" }), s.jsx("p", { className: "text-gray-500", children: "Select a patient to view details" })] }) })] }) })] })] }) }, $0 = () => { const { state: e, dispatch: t } = Ye(), { patients: n, prescriptions: r } = e, [l, i] = N.useState(null), [a, u] = N.useState(""), [o, c] = N.useState({ chiefComplaint: "", vitals: { temperature: "", bloodPressure: "", heartRate: "", respiratoryRate: "", oxygenSaturation: "" }, diagnosis: "", treatment: "", medications: [{ name: "", dosage: "", frequency: "", duration: "", quantity: 1 }], instructions: "" }), m = n.filter(d => d.status === "registered" || d.status === "in-treatment").filter(d => `${d.firstName} ${d.lastName}`.toLowerCase().includes(a.toLowerCase()) || d.phone.includes(a)), h = l ? n.find(d => d.id === l) : null, w = d => { i(d); const p = n.find(v => v.id === d); p && p.status === "registered" && t({ type: "UPDATE_PATIENT", payload: { ...p, status: "in-treatment", currentDepartment: "Nursing Station" } }) }, y = (d, p, v) => { const j = [...o.medications]; j[d] = { ...j[d], [p]: v }, c({ ...o, medications: j }) }, x = () => { c({ ...o, medications: [...o.medications, { name: "", dosage: "", frequency: "", duration: "", quantity: 1 }] }) }, k = d => { const p = o.medications.filter((v, j) => j !== d); c({ ...o, medications: p }) }, f = d => { if (d.preventDefault(), !h) return; if (o.medications.some(v => v.name)) { const v = { id: kn(), patientId: h.id, doctorId: "doctor1", doctorName: "Dr. Michael Smith", medications: o.medications.filter(j => j.name), instructions: o.instructions, dateIssued: new Date().toISOString(), status: "pending" }; t({ type: "ADD_PRESCRIPTION", payload: v }) } const p = { ...h, status: o.medications.some(v => v.name) ? "awaiting-pharmacy" : "awaiting-billing", currentDepartment: o.medications.some(v => v.name) ? "Pharmacy" : "Billing" }; t({ type: "UPDATE_PATIENT", payload: p }), c({ chiefComplaint: "", vitals: { temperature: "", bloodPressure: "", heartRate: "", respiratoryRate: "", oxygenSaturation: "" }, diagnosis: "", treatment: "", medications: [{ name: "", dosage: "", frequency: "", duration: "", quantity: 1 }], instructions: "" }), i(null), alert("Treatment completed successfully!") }; return s.jsxs("div", { className: "p-6 max-w-7xl mx-auto", children: [s.jsxs("div", { className: "mb-8", children: [s.jsxs("div", { className: "flex items-center space-x-3 mb-4", children: [s.jsx("div", { className: "flex items-center justify-center w-10 h-10 bg-green-100 rounded-lg", children: s.jsx(nr, { className: "w-5 h-5 text-green-600" }) }), s.jsxs("div", { children: [s.jsx("h1", { className: "text-2xl font-bold text-gray-900", children: "Nursing Station" }), s.jsx("p", { className: "text-gray-600", children: "Patient care and treatment management" })] })] }), s.jsxs("div", { className: "relative", children: [s.jsx(Zl, { className: "absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" }), s.jsx("input", { type: "text", placeholder: "Search patients by name or phone...", value: a, onChange: d => u(d.target.value), className: "w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" })] })] }), s.jsxs("div", { className: "grid grid-cols-1 lg:grid-cols-3 gap-8", children: [s.jsx("div", { className: "lg:col-span-1", children: s.jsxs("div", { className: "bg-white rounded-xl shadow-md border border-gray-200", children: [s.jsx("div", { className: "px-6 py-4 border-b border-gray-200", children: s.jsxs("h3", { className: "text-lg font-semibold text-gray-900", children: ["Patient Queue (", m.length, ")"] }) }), s.jsx("div", { className: "divide-y divide-gray-200 max-h-96 overflow-y-auto", children: m.length === 0 ? s.jsxs("div", { className: "p-6 text-center", children: [s.jsx(Sd, { className: "w-8 h-8 text-gray-400 mx-auto mb-2" }), s.jsx("p", { className: "text-gray-500", children: "No patients in queue" })] }) : m.map(d => s.jsx("div", { className: `p-4 hover:bg-gray-50 cursor-pointer transition-colors ${l === d.id ? "bg-green-50 border-l-4 border-green-500" : ""}`, onClick: () => w(d.id), children: s.jsxs("div", { className: "flex items-center justify-between", children: [s.jsxs("div", { children: [s.jsxs("h4", { className: "font-medium text-gray-900", children: [d.firstName, " ", d.lastName] }), s.jsx("p", { className: "text-sm text-gray-600", children: d.phone }), s.jsxs("p", { className: "text-xs text-gray-500", children: ["Registered: ", pe(new Date(d.registrationTime), "HH:mm")] })] }), s.jsx("div", { className: "text-right", children: s.jsx("span", { className: `inline-flex px-2 py-1 text-xs font-medium rounded-full ${d.status === "registered" ? "bg-blue-100 text-blue-800" : "bg-yellow-100 text-yellow-800"}`, children: d.status === "registered" ? "Waiting" : "In Treatment" }) })] }) }, d.id)) })] }) }), s.jsx("div", { className: "lg:col-span-2", children: h ? s.jsxs("div", { className: "bg-white rounded-xl shadow-md border border-gray-200", children: [s.jsx("div", { className: "px-6 py-4 border-b border-gray-200", children: s.jsxs("div", { className: "flex items-center justify-between", children: [s.jsxs("div", { children: [s.jsxs("h3", { className: "text-lg font-semibold text-gray-900", children: ["Treatment Record - ", h.firstName, " ", h.lastName] }), s.jsxs("p", { className: "text-sm text-gray-600", children: ["DOB: ", pe(new Date(h.dateOfBirth), "MMM dd, yyyy"), " • Phone: ", h.phone] })] }), s.jsxs("div", { className: "text-right", children: [s.jsxs("p", { className: "text-sm font-medium text-gray-900", children: ["Patient ID: ", h.id.slice(-8)] }), s.jsxs("p", { className: "text-xs text-gray-500", children: ["Registered: ", pe(new Date(h.registrationTime), "MMM dd, yyyy HH:mm")] })] })] }) }), s.jsxs("form", { onSubmit: f, className: "p-6 space-y-6", children: [s.jsxs("div", { children: [s.jsxs("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: ["Chief Complaint ", s.jsx("span", { className: "text-red-500", children: "*" })] }), s.jsx("textarea", { value: o.chiefComplaint, onChange: d => c({ ...o, chiefComplaint: d.target.value }), required: !0, rows: 3, className: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500", placeholder: "Patient's primary concern or symptoms..." })] }), s.jsxs("div", { children: [s.jsx("h4", { className: "text-lg font-medium text-gray-900 mb-4", children: "Vital Signs" }), s.jsxs("div", { className: "grid grid-cols-2 md:grid-cols-3 gap-4", children: [s.jsxs("div", { children: [s.jsx("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: "Temperature (°F)" }), s.jsx("input", { type: "number", step: "0.1", value: o.vitals.temperature, onChange: d => c({ ...o, vitals: { ...o.vitals, temperature: d.target.value } }), className: "w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" })] }), s.jsxs("div", { children: [s.jsx("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: "Blood Pressure" }), s.jsx("input", { type: "text", value: o.vitals.bloodPressure, onChange: d => c({ ...o, vitals: { ...o.vitals, bloodPressure: d.target.value } }), placeholder: "120/80", className: "w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" })] }), s.jsxs("div", { children: [s.jsx("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: "Heart Rate (bpm)" }), s.jsx("input", { type: "number", value: o.vitals.heartRate, onChange: d => c({ ...o, vitals: { ...o.vitals, heartRate: d.target.value } }), className: "w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" })] }), s.jsxs("div", { children: [s.jsx("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: "Respiratory Rate" }), s.jsx("input", { type: "number", value: o.vitals.respiratoryRate, onChange: d => c({ ...o, vitals: { ...o.vitals, respiratoryRate: d.target.value } }), className: "w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" })] }), s.jsxs("div", { children: [s.jsx("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: "O2 Saturation (%)" }), s.jsx("input", { type: "number", min: "0", max: "100", value: o.vitals.oxygenSaturation, onChange: d => c({ ...o, vitals: { ...o.vitals, oxygenSaturation: d.target.value } }), className: "w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" })] })] })] }), s.jsxs("div", { children: [s.jsxs("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: ["Diagnosis ", s.jsx("span", { className: "text-red-500", children: "*" })] }), s.jsx("textarea", { value: o.diagnosis, onChange: d => c({ ...o, diagnosis: d.target.value }), required: !0, rows: 3, className: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500", placeholder: "Primary diagnosis and any secondary conditions..." })] }), s.jsxs("div", { children: [s.jsxs("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: ["Treatment Plan ", s.jsx("span", { className: "text-red-500", children: "*" })] }), s.jsx("textarea", { value: o.treatment, onChange: d => c({ ...o, treatment: d.target.value }), required: !0, rows: 3, className: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500", placeholder: "Treatment plan and procedures performed..." })] }), s.jsxs("div", { children: [s.jsxs("div", { className: "flex items-center justify-between mb-4", children: [s.jsx("h4", { className: "text-lg font-medium text-gray-900", children: "Medications" }), s.jsxs("button", { type: "button", onClick: x, className: "flex items-center space-x-2 px-3 py-2 text-sm font-medium text-green-600 hover:text-green-700 hover:bg-green-50 rounded-lg transition-colors", children: [s.jsx(Ut, { className: "w-4 h-4" }), s.jsx("span", { children: "Add Medication" })] })] }), o.medications.map((d, p) => s.jsx("div", { className: "bg-gray-50 p-4 rounded-lg mb-4", children: s.jsxs("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4", children: [s.jsxs("div", { children: [s.jsx("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: "Medication Name" }), s.jsx("input", { type: "text", value: d.name, onChange: v => y(p, "name", v.target.value), className: "w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" })] }), s.jsxs("div", { children: [s.jsx("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: "Dosage" }), s.jsx("input", { type: "text", value: d.dosage, onChange: v => y(p, "dosage", v.target.value), placeholder: "e.g., 500mg", className: "w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" })] }), s.jsxs("div", { children: [s.jsx("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: "Frequency" }), s.jsx("input", { type: "text", value: d.frequency, onChange: v => y(p, "frequency", v.target.value), placeholder: "e.g., Twice daily", className: "w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" })] }), s.jsxs("div", { children: [s.jsx("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: "Duration" }), s.jsx("input", { type: "text", value: d.duration, onChange: v => y(p, "duration", v.target.value), placeholder: "e.g., 7 days", className: "w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" })] }), s.jsxs("div", { children: [s.jsx("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: "Quantity" }), s.jsx("input", { type: "number", min: "1", value: d.quantity, onChange: v => y(p, "quantity", parseInt(v.target.value)), className: "w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" })] }), s.jsx("div", { className: "flex items-end", children: s.jsx("button", { type: "button", onClick: () => k(p), className: "w-full px-3 py-2 text-sm font-medium text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors", children: "Remove" }) })] }) }, p))] }), s.jsxs("div", { children: [s.jsx("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: "Instructions for Patient" }), s.jsx("textarea", { value: o.instructions, onChange: d => c({ ...o, instructions: d.target.value }), rows: 3, className: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500", placeholder: "Additional instructions for the patient..." })] }), s.jsxs("div", { className: "flex justify-end space-x-4 pt-6 border-t border-gray-200", children: [s.jsx("button", { type: "button", onClick: () => i(null), className: "px-6 py-3 border border-gray-300 rounded-lg font-medium text-gray-700 hover:bg-gray-50 transition-colors", children: "Cancel" }), s.jsxs("button", { type: "submit", className: "flex items-center space-x-2 px-6 py-3 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors", children: [s.jsx(ka, { className: "w-4 h-4" }), s.jsx("span", { children: "Complete Treatment" })] })] })] })] }) : s.jsx("div", { className: "bg-white rounded-xl shadow-md border border-gray-200 p-8", children: s.jsxs("div", { className: "text-center", children: [s.jsx(nr, { className: "w-16 h-16 text-gray-400 mx-auto mb-4" }), s.jsx("h3", { className: "text-lg font-medium text-gray-900 mb-2", children: "Select a Patient" }), s.jsx("p", { className: "text-gray-600", children: "Choose a patient from the queue to begin treatment" })] }) }) })] })] }) }, U0 = () => { const { state: e, dispatch: t } = Ye(), { prescriptions: n, patients: r } = e, [l, i] = N.useState(""), [a, u] = N.useState(null), o = n.filter(y => y.status === "pending"), c = n.filter(y => y.status === "dispensed"), g = o.filter(y => { const x = r.find(k => k.id === y.patientId); return x ? `${x.firstName} ${x.lastName}`.toLowerCase().includes(l.toLowerCase()) || x.phone.includes(l) || y.id.includes(l) : !1 }), m = a ? n.find(y => y.id === a) : null, h = m ? r.find(y => y.id === m.patientId) : null, w = () => { if (!m || !h) return; const y = { ...m, status: "dispensed" }; t({ type: "UPDATE_PRESCRIPTION", payload: y }); const x = { ...h, status: "awaiting-billing", currentDepartment: "Billing" }; t({ type: "UPDATE_PATIENT", payload: x }), t({ type: "ADD_AUDIT_LOG", payload: { id: kn(), userId: "pharmacist1", userRole: "pharmacist", action: "DISPENSE", resource: "Prescription", resourceId: m.id, timestamp: new Date().toISOString(), details: `Prescription dispensed for ${h.firstName} ${h.lastName}` } }), u(null), alert("Prescription dispensed successfully!") }; return s.jsxs("div", { className: "p-6 max-w-7xl mx-auto", children: [s.jsxs("div", { className: "mb-8", children: [s.jsxs("div", { className: "flex items-center space-x-3 mb-4", children: [s.jsx("div", { className: "flex items-center justify-center w-10 h-10 bg-orange-100 rounded-lg", children: s.jsx(Ut, { className: "w-5 h-5 text-orange-600" }) }), s.jsxs("div", { children: [s.jsx("h1", { className: "text-2xl font-bold text-gray-900", children: "Pharmacy Dashboard" }), s.jsx("p", { className: "text-gray-600", children: "Prescription management and medication dispensing" })] })] }), s.jsxs("div", { className: "relative", children: [s.jsx(Zl, { className: "absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" }), s.jsx("input", { type: "text", placeholder: "Search prescriptions by patient name, phone, or prescription ID...", value: l, onChange: y => i(y.target.value), className: "w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500" })] })] }), s.jsxs("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-6 mb-8", children: [s.jsx("div", { className: "bg-white rounded-xl p-6 shadow-md border border-gray-200", children: s.jsxs("div", { className: "flex items-center", children: [s.jsx("div", { className: "flex items-center justify-center w-12 h-12 bg-orange-100 rounded-lg", children: s.jsx(Nd, { className: "w-6 h-6 text-orange-600" }) }), s.jsxs("div", { className: "ml-4", children: [s.jsx("p", { className: "text-sm font-medium text-gray-600", children: "Pending Prescriptions" }), s.jsx("p", { className: "text-2xl font-bold text-gray-900", children: o.length })] })] }) }), s.jsx("div", { className: "bg-white rounded-xl p-6 shadow-md border border-gray-200", children: s.jsxs("div", { className: "flex items-center", children: [s.jsx("div", { className: "flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg", children: s.jsx(Wo, { className: "w-6 h-6 text-green-600" }) }), s.jsxs("div", { className: "ml-4", children: [s.jsx("p", { className: "text-sm font-medium text-gray-600", children: "Dispensed Today" }), s.jsx("p", { className: "text-2xl font-bold text-gray-900", children: c.length })] })] }) }), s.jsx("div", { className: "bg-white rounded-xl p-6 shadow-md border border-gray-200", children: s.jsxs("div", { className: "flex items-center", children: [s.jsx("div", { className: "flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg", children: s.jsx(jh, { className: "w-6 h-6 text-blue-600" }) }), s.jsxs("div", { className: "ml-4", children: [s.jsx("p", { className: "text-sm font-medium text-gray-600", children: "Total Medications" }), s.jsx("p", { className: "text-2xl font-bold text-gray-900", children: n.reduce((y, x) => y + x.medications.length, 0) })] })] }) })] }), s.jsxs("div", { className: "grid grid-cols-1 lg:grid-cols-3 gap-8", children: [s.jsx("div", { className: "lg:col-span-2", children: s.jsxs("div", { className: "bg-white rounded-xl shadow-md border border-gray-200", children: [s.jsx("div", { className: "px-6 py-4 border-b border-gray-200", children: s.jsxs("h3", { className: "text-lg font-semibold text-gray-900", children: ["Pending Prescriptions (", g.length, ")"] }) }), s.jsx("div", { className: "divide-y divide-gray-200 max-h-96 overflow-y-auto", children: g.length === 0 ? s.jsxs("div", { className: "p-8 text-center", children: [s.jsx(Ut, { className: "w-12 h-12 text-gray-400 mx-auto mb-4" }), s.jsx("p", { className: "text-gray-500", children: "No pending prescriptions" })] }) : g.map(y => { const x = r.find(k => k.id === y.patientId); return x ? s.jsx("div", { className: `p-6 hover:bg-gray-50 cursor-pointer transition-colors ${a === y.id ? "bg-orange-50 border-l-4 border-orange-500" : ""}`, onClick: () => u(y.id), children: s.jsxs("div", { className: "flex items-center justify-between", children: [s.jsxs("div", { className: "flex-1", children: [s.jsxs("div", { className: "flex items-center space-x-3", children: [s.jsxs("h4", { className: "text-lg font-medium text-gray-900", children: [x.firstName, " ", x.lastName] }), s.jsx("span", { className: "inline-flex px-2 py-1 text-xs font-medium rounded-full bg-orange-100 text-orange-800", children: "Pending" })] }), s.jsxs("div", { className: "mt-2 grid grid-cols-2 gap-4 text-sm text-gray-600", children: [s.jsxs("div", { children: [s.jsxs("p", { children: [s.jsx("strong", { children: "Prescription ID:" }), " ", y.id.slice(-8)] }), s.jsxs("p", { children: [s.jsx("strong", { children: "Phone:" }), " ", x.phone] })] }), s.jsxs("div", { children: [s.jsxs("p", { children: [s.jsx("strong", { children: "Doctor:" }), " ", y.doctorName] }), s.jsxs("p", { children: [s.jsx("strong", { children: "Date:" }), " ", pe(new Date(y.dateIssued), "MMM dd, HH:mm")] })] })] }), s.jsx("div", { className: "mt-2", children: s.jsxs("p", { className: "text-sm text-gray-600", children: [s.jsx("strong", { children: "Medications:" }), " ", y.medications.map(k => k.name).join(", ")] }) })] }), s.jsx("div", { className: "ml-4", children: s.jsxs("div", { className: "bg-orange-100 text-orange-800 px-3 py-2 rounded-lg text-center", children: [s.jsx("p", { className: "text-lg font-bold", children: y.medications.length }), s.jsx("p", { className: "text-xs", children: "Items" })] }) })] }) }, y.id) : null }) })] }) }), s.jsx("div", { className: "lg:col-span-1", children: s.jsxs("div", { className: "bg-white rounded-xl shadow-md border border-gray-200 sticky top-6", children: [s.jsx("div", { className: "px-6 py-4 border-b border-gray-200", children: s.jsx("h3", { className: "text-lg font-semibold text-gray-900", children: "Prescription Details" }) }), s.jsx("div", { className: "p-6", children: m && h ? s.jsxs("div", { className: "space-y-6", children: [s.jsxs("div", { children: [s.jsx("h4", { className: "text-sm font-medium text-gray-900 mb-3", children: "Patient Information" }), s.jsxs("div", { className: "space-y-2 text-sm", children: [s.jsxs("p", { children: [s.jsx("strong", { children: "Name:" }), " ", h.firstName, " ", h.lastName] }), s.jsxs("p", { children: [s.jsx("strong", { children: "DOB:" }), " ", pe(new Date(h.dateOfBirth), "MMM dd, yyyy")] }), s.jsxs("p", { children: [s.jsx("strong", { children: "Phone:" }), " ", h.phone] }), s.jsxs("p", { children: [s.jsx("strong", { children: "Allergies:" }), " ", h.allergies.length > 0 ? h.allergies.join(", ") : "None"] })] })] }), s.jsxs("div", { children: [s.jsx("h4", { className: "text-sm font-medium text-gray-900 mb-3", children: "Prescription Details" }), s.jsxs("div", { className: "space-y-2 text-sm", children: [s.jsxs("p", { children: [s.jsx("strong", { children: "Prescribed by:" }), " ", m.doctorName] }), s.jsxs("p", { children: [s.jsx("strong", { children: "Date:" }), " ", pe(new Date(m.dateIssued), "MMM dd, yyyy HH:mm")] }), s.jsxs("p", { children: [s.jsx("strong", { children: "Instructions:" }), " ", m.instructions || "None"] })] })] }), s.jsxs("div", { children: [s.jsx("h4", { className: "text-sm font-medium text-gray-900 mb-3", children: "Medications" }), s.jsx("div", { className: "space-y-3", children: m.medications.map((y, x) => s.jsxs("div", { className: "p-3 bg-gray-50 rounded-lg", children: [s.jsx("h5", { className: "font-medium text-gray-900", children: y.name }), s.jsxs("div", { className: "mt-1 text-sm text-gray-600", children: [s.jsxs("p", { children: [s.jsx("strong", { children: "Dosage:" }), " ", y.dosage] }), s.jsxs("p", { children: [s.jsx("strong", { children: "Frequency:" }), " ", y.frequency] }), s.jsxs("p", { children: [s.jsx("strong", { children: "Duration:" }), " ", y.duration] }), s.jsxs("p", { children: [s.jsx("strong", { children: "Quantity:" }), " ", y.quantity] })] })] }, x)) })] }), s.jsx("div", { className: "pt-4 border-t border-gray-200", children: s.jsxs("button", { onClick: w, className: "w-full flex items-center justify-center space-x-2 bg-orange-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-orange-700 transition-colors", children: [s.jsx(Wo, { className: "w-4 h-4" }), s.jsx("span", { children: "Dispense Prescription" })] }) })] }) : s.jsxs("div", { className: "text-center py-8", children: [s.jsx(Ut, { className: "w-12 h-12 text-gray-400 mx-auto mb-4" }), s.jsx("p", { className: "text-gray-500", children: "Select a prescription to view details" })] }) })] }) })] })] }) }, A0 = () => {
    const { state: e, dispatch: t } = Ye(), { patients: n, prescriptions: r, visits: l } = e, [i, a] = N.useState(""), [u, o] = N.useState(null), c = n.filter(f => f.status === "awaiting-billing"), g = c.filter(f => `${f.firstName} ${f.lastName}`.toLowerCase().includes(i.toLowerCase()) || f.phone.includes(i)), m = u ? n.find(f => f.id === u) : null, h = m ? r.filter(f => f.patientId === m.id) : [], w = () => { if (!m) return { consultation: 0, medications: 0, total: 0 }; const f = 150, d = h.reduce((p, v) => p + v.medications.reduce((j, P) => j + P.quantity * 25, 0), 0); return { consultation: f, medications: d, total: f + d } }, y = () => { if (!m) return; const f = w(), d = { id: kn(), patientId: m.id, dateTime: new Date().toISOString(), department: "General Medicine", chiefComplaint: "Medical consultation", diagnosis: "Consultation completed", treatment: "Treatment provided", prescriptions: h, totalCost: f.total, status: "completed" }; t({ type: "ADD_VISIT", payload: d }); const p = { ...m, status: "completed", currentDepartment: "Completed" }; t({ type: "UPDATE_PATIENT", payload: p }), t({ type: "ADD_AUDIT_LOG", payload: { id: kn(), userId: "billing1", userRole: "billing", action: "COMPLETE_BILLING", resource: "Visit", resourceId: d.id, timestamp: new Date().toISOString(), details: `Billing completed for ${m.firstName} ${m.lastName} - $${f.total}` } }), o(null), alert("Payment processed successfully! Visit summary has been generated.") }, x = () => {
        if (!m) return; const f = w(), d = `
HOSPITAL VISIT SUMMARY
=====================

Patient Information:
- Name: ${m.firstName} ${m.lastName}
- Date of Birth: ${pe(new Date(m.dateOfBirth), "MMM dd, yyyy")}
- Phone: ${m.phone}
- Email: ${m.email}

Visit Details:
- Date: ${pe(new Date, "MMM dd, yyyy")}
- Time: ${pe(new Date, "HH:mm")}
- Department: ${m.currentDepartment}

Billing Summary:
- Consultation Fee: $${f.consultation}
- Medications: $${f.medications}
- Total Amount: $${f.total}

Prescribed Medications:
${h.map(P => P.medications.map(C => `- ${C.name} (${C.dosage}) - ${C.frequency} for ${C.duration}`).join(`
`)).join(`
`)}

Instructions: ${h.map(P => P.instructions).filter(Boolean).join(" ")}

Thank you for choosing MedFlow Hospital!
    `, p = new Blob([d], { type: "text/plain" }), v = URL.createObjectURL(p), j = document.createElement("a"); j.href = v, j.download = `visit-summary-${m.firstName}-${m.lastName}-${pe(new Date, "yyyy-MM-dd")}.txt`, document.body.appendChild(j), j.click(), document.body.removeChild(j), URL.revokeObjectURL(v)
    }, k = w(); return s.jsxs("div", { className: "p-6 max-w-7xl mx-auto", children: [s.jsxs("div", { className: "mb-8", children: [s.jsxs("div", { className: "flex items-center space-x-3 mb-4", children: [s.jsx("div", { className: "flex items-center justify-center w-10 h-10 bg-purple-100 rounded-lg", children: s.jsx(Ot, { className: "w-5 h-5 text-purple-600" }) }), s.jsxs("div", { children: [s.jsx("h1", { className: "text-2xl font-bold text-gray-900", children: "Billing Dashboard" }), s.jsx("p", { className: "text-gray-600", children: "Patient billing and payment processing" })] })] }), s.jsxs("div", { className: "relative", children: [s.jsx(Zl, { className: "absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" }), s.jsx("input", { type: "text", placeholder: "Search patients by name or phone...", value: i, onChange: f => a(f.target.value), className: "w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500" })] })] }), s.jsx("div", { className: "bg-white rounded-xl p-6 shadow-md border border-gray-200 mb-8", children: s.jsxs("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-6", children: [s.jsxs("div", { className: "flex items-center", children: [s.jsx("div", { className: "flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg", children: s.jsx(Ot, { className: "w-6 h-6 text-purple-600" }) }), s.jsxs("div", { className: "ml-4", children: [s.jsx("p", { className: "text-sm font-medium text-gray-600", children: "Pending Bills" }), s.jsx("p", { className: "text-2xl font-bold text-gray-900", children: c.length })] })] }), s.jsxs("div", { className: "flex items-center", children: [s.jsx("div", { className: "flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg", children: s.jsx(xh, { className: "w-6 h-6 text-green-600" }) }), s.jsxs("div", { className: "ml-4", children: [s.jsx("p", { className: "text-sm font-medium text-gray-600", children: "Completed Visits" }), s.jsx("p", { className: "text-2xl font-bold text-gray-900", children: l.filter(f => f.status === "completed").length })] })] }), s.jsxs("div", { className: "flex items-center", children: [s.jsx("div", { className: "flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg", children: s.jsx(Sh, { className: "w-6 h-6 text-blue-600" }) }), s.jsxs("div", { className: "ml-4", children: [s.jsx("p", { className: "text-sm font-medium text-gray-600", children: "Total Revenue" }), s.jsxs("p", { className: "text-2xl font-bold text-gray-900", children: ["$", l.reduce((f, d) => f + d.totalCost, 0).toLocaleString()] })] })] })] }) }), s.jsxs("div", { className: "grid grid-cols-1 lg:grid-cols-3 gap-8", children: [s.jsx("div", { className: "lg:col-span-2", children: s.jsxs("div", { className: "bg-white rounded-xl shadow-md border border-gray-200", children: [s.jsx("div", { className: "px-6 py-4 border-b border-gray-200", children: s.jsxs("h3", { className: "text-lg font-semibold text-gray-900", children: ["Patients Awaiting Billing (", g.length, ")"] }) }), s.jsx("div", { className: "divide-y divide-gray-200 max-h-96 overflow-y-auto", children: g.length === 0 ? s.jsxs("div", { className: "p-8 text-center", children: [s.jsx(Ot, { className: "w-12 h-12 text-gray-400 mx-auto mb-4" }), s.jsx("p", { className: "text-gray-500", children: "No patients awaiting billing" })] }) : g.map(f => { const d = r.filter(v => v.patientId === f.id), p = 150 + d.reduce((v, j) => v + j.medications.reduce((P, C) => P + C.quantity * 25, 0), 0); return s.jsx("div", { className: `p-6 hover:bg-gray-50 cursor-pointer transition-colors ${u === f.id ? "bg-purple-50 border-l-4 border-purple-500" : ""}`, onClick: () => o(f.id), children: s.jsxs("div", { className: "flex items-center justify-between", children: [s.jsxs("div", { className: "flex-1", children: [s.jsxs("div", { className: "flex items-center space-x-3", children: [s.jsxs("h4", { className: "text-lg font-medium text-gray-900", children: [f.firstName, " ", f.lastName] }), s.jsx("span", { className: "inline-flex px-2 py-1 text-xs font-medium rounded-full bg-purple-100 text-purple-800", children: "Awaiting Payment" })] }), s.jsxs("div", { className: "mt-2 grid grid-cols-2 gap-4 text-sm text-gray-600", children: [s.jsxs("div", { children: [s.jsxs("p", { children: [s.jsx("strong", { children: "Patient ID:" }), " ", f.id.slice(-8)] }), s.jsxs("p", { children: [s.jsx("strong", { children: "Phone:" }), " ", f.phone] })] }), s.jsxs("div", { children: [s.jsxs("p", { children: [s.jsx("strong", { children: "Department:" }), " ", f.currentDepartment] }), s.jsxs("p", { children: [s.jsx("strong", { children: "Medications:" }), " ", d.reduce((v, j) => v + j.medications.length, 0), " items"] })] })] })] }), s.jsx("div", { className: "ml-4", children: s.jsxs("div", { className: "bg-purple-100 text-purple-800 px-3 py-2 rounded-lg text-center", children: [s.jsxs("p", { className: "text-lg font-bold", children: ["$", p] }), s.jsx("p", { className: "text-xs", children: "Estimated" })] }) })] }) }, f.id) }) })] }) }), s.jsx("div", { className: "lg:col-span-1", children: s.jsxs("div", { className: "bg-white rounded-xl shadow-md border border-gray-200 sticky top-6", children: [s.jsx("div", { className: "px-6 py-4 border-b border-gray-200", children: s.jsx("h3", { className: "text-lg font-semibold text-gray-900", children: "Billing Details" }) }), s.jsx("div", { className: "p-6", children: m ? s.jsxs("div", { className: "space-y-6", children: [s.jsxs("div", { children: [s.jsx("h4", { className: "text-sm font-medium text-gray-900 mb-3", children: "Patient Information" }), s.jsxs("div", { className: "space-y-2 text-sm", children: [s.jsxs("p", { children: [s.jsx("strong", { children: "Name:" }), " ", m.firstName, " ", m.lastName] }), s.jsxs("p", { children: [s.jsx("strong", { children: "DOB:" }), " ", pe(new Date(m.dateOfBirth), "MMM dd, yyyy")] }), s.jsxs("p", { children: [s.jsx("strong", { children: "Phone:" }), " ", m.phone] }), s.jsxs("p", { children: [s.jsx("strong", { children: "Insurance:" }), " ", m.insuranceNumber || "None"] })] })] }), s.jsxs("div", { children: [s.jsx("h4", { className: "text-sm font-medium text-gray-900 mb-3", children: "Service Details" }), s.jsxs("div", { className: "space-y-3", children: [s.jsxs("div", { className: "flex justify-between items-center p-3 bg-gray-50 rounded-lg", children: [s.jsx("span", { className: "text-sm", children: "Consultation Fee" }), s.jsxs("span", { className: "font-medium", children: ["$", k.consultation] })] }), s.jsxs("div", { className: "flex justify-between items-center p-3 bg-gray-50 rounded-lg", children: [s.jsxs("span", { className: "text-sm", children: ["Medications (", h.reduce((f, d) => f + d.medications.length, 0), " items)"] }), s.jsxs("span", { className: "font-medium", children: ["$", k.medications] })] })] })] }), s.jsx("div", { className: "border-t border-gray-200 pt-4", children: s.jsxs("div", { className: "flex justify-between items-center", children: [s.jsx("span", { className: "text-lg font-semibold text-gray-900", children: "Total Amount" }), s.jsxs("span", { className: "text-2xl font-bold text-purple-600", children: ["$", k.total] })] }) }), s.jsxs("div", { className: "space-y-3", children: [s.jsxs("button", { onClick: y, className: "w-full flex items-center justify-center space-x-2 bg-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-purple-700 transition-colors", children: [s.jsx(Ot, { className: "w-4 h-4" }), s.jsx("span", { children: "Process Payment" })] }), s.jsxs("button", { onClick: x, className: "w-full flex items-center justify-center space-x-2 border border-purple-600 text-purple-600 py-3 px-4 rounded-lg font-medium hover:bg-purple-50 transition-colors", children: [s.jsx(ka, { className: "w-4 h-4" }), s.jsx("span", { children: "Generate Summary" })] })] }), h.length > 0 && s.jsxs("div", { children: [s.jsx("h4", { className: "text-sm font-medium text-gray-900 mb-3", children: "Prescribed Medications" }), s.jsx("div", { className: "space-y-2 max-h-32 overflow-y-auto", children: h.map(f => f.medications.map((d, p) => s.jsxs("div", { className: "text-sm p-2 bg-gray-50 rounded", children: [s.jsx("p", { className: "font-medium", children: d.name }), s.jsxs("p", { className: "text-gray-600", children: [d.dosage, " - Qty: ", d.quantity] })] }, p))) })] })] }) : s.jsxs("div", { className: "text-center py-8", children: [s.jsx(Ot, { className: "w-12 h-12 text-gray-400 mx-auto mb-4" }), s.jsx("p", { className: "text-gray-500", children: "Select a patient to view billing details" })] }) })] }) })] })] })
}, W0 = () => { const { state: e, dispatch: t } = Ye(), { currentUser: n } = e, r = i => { t({ type: "SET_CURRENT_USER", payload: i }) }, l = () => { t({ type: "SET_CURRENT_USER", payload: null }) }; return n ? s.jsx(rh, { children: s.jsxs("div", { className: "min-h-screen bg-gray-50", children: [s.jsx(bh, { onLogout: l }), s.jsxs("div", { className: "flex", children: [s.jsx(Mh, {}), s.jsx("main", { className: "flex-1", children: s.jsxs(Gp, { children: [s.jsx(Ae, { path: "/", element: s.jsx(Yp, { to: "/dashboard", replace: !0 }) }), s.jsx(Ae, { path: "/dashboard", element: s.jsx(Th, {}) }), s.jsx(Ae, { path: "/register", element: s.jsx(Dh, {}) }), s.jsx(Ae, { path: "/patients", element: s.jsx(F0, {}) }), s.jsx(Ae, { path: "/nursing", element: s.jsx($0, {}) }), s.jsx(Ae, { path: "/pharmacy", element: s.jsx(U0, {}) }), s.jsx(Ae, { path: "/billing", element: s.jsx(A0, {}) }), s.jsx(Ae, { path: "/audit", element: s.jsxs("div", { className: "p-6", children: [s.jsx("h1", { children: "Audit Logs" }), s.jsx("p", { children: "Coming soon..." })] }) }), s.jsx(Ae, { path: "/settings", element: s.jsxs("div", { className: "p-6", children: [s.jsx("h1", { children: "Settings" }), s.jsx("p", { children: "Coming soon..." })] }) })] }) })] })] }) }) : s.jsx(_h, { onLogin: r }) }; function B0() { return s.jsx(mh, { children: s.jsx(W0, {}) }) } ad(document.getElementById("root")).render(s.jsx(N.StrictMode, { children: s.jsx(B0, {}) }));
